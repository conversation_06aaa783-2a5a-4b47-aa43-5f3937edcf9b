spring.application.name=Peppol Validation
#spring.mvc.servlet.path=/peppol-validation

logging.level.root=info
logging.level.org.springframework.web=debug

spring.config.additional-location=classpath:/messages/messages_errorcode.properties
spring.messages.basename=messages/messages_errorcode, messages/messages_validation

server.port=8085

auth.token.header=api-key
auth.token.apikey=qwerty

enable.swagger = true
api.key.auth.enabled=true

spring.web.resources.static-locations=classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/

springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*******************************************
spring.datasource.username=root
spring.datasource.password=root
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true