# errormap.properties
#
# This properties file contains mappings between exception class names and corresponding error codes.
# These error codes are used throughout the application to represent specific error conditions
# and are referenced in the code, such as in exception handling and logging.
# This file allows for centralized management of error codes, making it easier to update or localize error messages if needed.
peppol.validation.GenericException=peppol.vs.000
peppol.validation.ErrorListMappingException=peppol.vs.001
peppol.validation.FileIOException=peppol.vs.002
peppol.validation.ValidationExecutionException=peppol.vs.003
peppol.validation.RecordNotFoundException=peppol.vs.004
peppol.validation.BadCredentialsException peppol.vs.005