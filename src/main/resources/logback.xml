<?xml version="1.0" encoding="UTF-8"?>

<!-- For assistance related to logback-translator or configuration  -->
<!-- files in general, please contact the logback user mailing list -->
<!-- at http://www.qos.ch/mailman/listinfo/logback-user             -->
<!--                                                                -->
<!-- For professional support please see                            -->
<!--    http://www.qos.ch/shop/products/professionalSupport         -->
<!--                                                                -->
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss}){faint} %clr([ ${springAppName} ]) %clr([%thread]){magenta} %clr(%-5level) %logger{0}-%M:%L - %msg%n"/>
    <property name="FILE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss} %-5p [ ${springAppName} ] %c{0}:%L - %m%n"/>
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <Target>System.out</Target>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--See also http://logback.qos.ch/manual/appenders.html#RollingFileAppender-->
        <append>true</append>
        <File>/home/<USER>/logs/peppol-validation-service/application.log</File>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <maxIndex>50</maxIndex>
            <FileNamePattern>/home/<USER>/logs/peppol-validation-service/application.log.%i</FileNamePattern>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>10MB</MaxFileSize>
        </triggeringPolicy>
    </appender>
    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--See also http://logback.qos.ch/manual/appenders.html#RollingFileAppender-->
        <append>true</append>
        <File>/home/<USER>/logs/peppol-validation-service/error.log</File>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <maxIndex>50</maxIndex>
            <FileNamePattern>/home/<USER>/logs/peppol-validation-service/error.log.%i</FileNamePattern>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>10MB</MaxFileSize>
        </triggeringPolicy>
    </appender>
    <logger name="org.hibernate.SQL" level="ERROR"/>
    <logger name="org.springframework.data.mongodb.core.MongoDbUtils" level="INFO"/>
    <logger name="com.mchange.v2" level="ERROR"/>
    <logger name="org.springframework.orm" level="error"/>
    <logger name="org.hibernate.cache" level="info"/>
    <logger name="org.hibernate.jdbc" level="ERROR"/>
    <logger name="com.zaxxer" level="DEBUG"/>
    <logger name="org.hibernate.type" level="ERROR"/>
    <logger name="org.hibernate.tool.hbm2ddl" level="warn"/>
    <logger name="org.springframework.data.mongodb.core.MongoTemplate" level="INFO"/>
    <logger name="org.hibernate.hql.ast.AST" level="info"/>
    <logger name="org.apache.http" level="warn"/>
    <logger name="httpclient.wire.content" level="WARN"/>
    <logger name="httpclient.wire.header" level="WARN"/>
    <logger name="org.hibernate.hql" level="ERROR"/>
    <root level="info">
        <appender-ref ref="stdout"/>
        <appender-ref ref="file"/>
        <appender-ref ref="error"/>
    </root>
</configuration>