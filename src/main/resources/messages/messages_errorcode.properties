# messages_errorcode.properties
#
# This properties file contains mappings between error codes and corresponding user-friendly error messages.
# These messages are used throughout the application to provide meaningful feedback to users when exceptions occur.
# By centralizing these messages in this file, it becomes easier to manage, update, and localize the error messages
# as needed without changing the underlying code.
#   <AUTHOR>
#   @since 1.0
#   @version 1.0

peppol.vs.000=Something Went Wrong
peppol.vs.001=Error List Mapping Failed
peppol.vs.002=File Operation Failed
peppol.vs.003=Validation Failed
peppol.vs.004=Record Not Found
peppol.vs.005=Invalid Credential