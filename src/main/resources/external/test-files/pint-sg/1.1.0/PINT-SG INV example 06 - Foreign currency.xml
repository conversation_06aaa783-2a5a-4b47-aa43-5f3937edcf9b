<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:ccts="urn:un:unece:uncefact:documentation:2" xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" xmlns:qdt="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" xmlns:udt="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

<!-- 
Singapore invoice

Foreign currency
-->

	<cbc:UBLVersionID>2.1</cbc:UBLVersionID>
	<cbc:CustomizationID>urn:peppol:pint:billing-1@sg-1</cbc:CustomizationID> <!-- BT-24 -->
	<cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID> <!-- BT-23 -->
	<cbc:ID>F012345</cbc:ID> <!-- BT-1 -->
	<cbc:UUID>2523149b-cb40-4b32-9004-debca57b80ee</cbc:UUID>
	<cbc:IssueDate>2024-01-01</cbc:IssueDate> <!-- BT-2 -->
	<cbc:DueDate>2024-02-01</cbc:DueDate> <!-- BT-9 -->
	<cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode> <!-- BT-3 -->
	<cbc:DocumentCurrencyCode>USD</cbc:DocumentCurrencyCode> <!-- BT-5 -->
	<cbc:TaxCurrencyCode>SGD</cbc:TaxCurrencyCode>
	<cbc:BuyerReference>123</cbc:BuyerReference> <!-- BT-10 -->
	<cac:AdditionalDocumentReference>
		<cbc:ID>SGD</cbc:ID>
		<cbc:DocumentTypeCode>sgdtotal-excl-gst</cbc:DocumentTypeCode>
		<cbc:DocumentDescription>45785.25</cbc:DocumentDescription>
	</cac:AdditionalDocumentReference>
	<cac:AdditionalDocumentReference>
		<cbc:ID>SGD</cbc:ID>
		<cbc:DocumentTypeCode>sgdtotal-incl-gst</cbc:DocumentTypeCode>
		<cbc:DocumentDescription>49213.37</cbc:DocumentDescription>
	</cac:AdditionalDocumentReference>
	<cac:AccountingSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0195">SGUEN**********</cbc:EndpointID> <!-- BT-34, BT-34-1 -->
			<cac:PartyIdentification>
				<cbc:ID> schemeID="0195">**********</cbc:ID> <!-- BT-29, BT-29-1 -->
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>Mainstreet 112</cbc:StreetName> <!-- BT-35 -->
				<cbc:AdditionalStreetName>Building 3</cbc:AdditionalStreetName> <!-- BT-36 -->
				<cbc:CityName>Singapore</cbc:CityName> <!-- BT-37 -->
				<cbc:PostalZone>1000</cbc:PostalZone> <!-- BT-38 -->
				<cbc:CountrySubentity>Singapore</cbc:CountrySubentity> <!-- BT-39 -->
				<cac:Country>
					<cbc:IdentificationCode>SG</cbc:IdentificationCode> <!-- BT-40 -->
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme>
				<cbc:CompanyID>M2-1234567-K</cbc:CompanyID> <!-- BT-31 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-31, qualifier -->
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Gallery Photo Supplier</cbc:RegistrationName> <!-- BT-27 -->
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingSupplierParty>
	<cac:AccountingCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0195">SGUEN**********</cbc:EndpointID> <!-- BT-49, BT-49-1 -->
			<cac:PartyIdentification>
				<cbc:ID schemeID="0195">345KS5324</cbc:ID> <!-- BT-46, BT-46-1 -->
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>Central road 56</cbc:StreetName> <!-- BT-50 -->
				<cbc:AdditionalStreetName>Second floor</cbc:AdditionalStreetName> <!-- BT-51 -->
				<cbc:CityName>Singapore</cbc:CityName> <!-- BT-52 -->
				<cbc:PostalZone>101</cbc:PostalZone> <!-- BT-53 -->
				<cbc:CountrySubentity>Singapore</cbc:CountrySubentity> <!-- BT-54 -->
				<cac:Country>
					<cbc:IdentificationCode>SG</cbc:IdentificationCode> <!-- BT-55 -->
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>IMDA</cbc:RegistrationName> <!-- BT-45 -->
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingCustomerParty>
	<cac:PaymentMeans>
		<cbc:PaymentMeansCode name="Bank transfer">30</cbc:PaymentMeansCode> <!-- BT-82, BT-81 -->
		<cbc:PaymentID>gr12345</cbc:PaymentID> <!-- BT-83 -->
		<cac:PayeeFinancialAccount>
			<cbc:ID>************</cbc:ID> <!-- BT-84 -->
			<cbc:Name>Payee current account</cbc:Name> <!-- BT-85 -->
			<cac:FinancialInstitutionBranch>
				<cbc:ID>ICDLOG</cbc:ID> <!-- BT-86 -->
			</cac:FinancialInstitutionBranch>
		</cac:PayeeFinancialAccount>
	</cac:PaymentMeans>
	<cac:PaymentTerms>
		<cbc:Note>Late fees of 1% charged from due date</cbc:Note> <!-- BT-20 -->
	</cac:PaymentTerms>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="USD">2539.35</cbc:TaxAmount> <!-- BT-110 -->
		<cac:TaxSubtotal>
			<cbc:TaxableAmount currencyID="USD">28215.00</cbc:TaxableAmount> <!-- BT-116 -->
			<cbc:TaxAmount currencyID="USD">2539.35</cbc:TaxAmount> <!-- BT-117 -->
			<cac:TaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-118 -->
				<cbc:Percent>9</cbc:Percent> <!-- BT-119 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-118, qualifier -->
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
		<cac:TaxSubtotal>
			<cbc:TaxableAmount currencyID="USD">5700.00</cbc:TaxableAmount> <!-- BT-116 -->
			<cbc:TaxAmount currencyID="USD">0</cbc:TaxAmount> <!-- BT-117 -->
			<cac:TaxCategory>
				<cbc:ID>ZR</cbc:ID> <!-- BT-118 -->
				<cbc:Percent>0</cbc:Percent> <!-- BT-119 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-118, qualifier -->
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
	</cac:TaxTotal>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="SGD">3428.12</cbc:TaxAmount> <!-- BT-110 -->
	</cac:TaxTotal>
	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="USD">33915.00</cbc:LineExtensionAmount> <!-- BT-106 -->
		<cbc:TaxExclusiveAmount currencyID="USD">33915.00</cbc:TaxExclusiveAmount> <!-- BT-109 -->
		<cbc:TaxInclusiveAmount currencyID="USD">36454.35</cbc:TaxInclusiveAmount> <!-- BT-112 -->
		<cbc:PayableAmount currencyID="USD">36454.35</cbc:PayableAmount> <!-- BT-115 -->
	</cac:LegalMonetaryTotal>
	<cac:InvoiceLine>
		<cbc:ID>1</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">10</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="USD">855.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-138, BT-143 -->
			<cbc:Amount currencyID="USD">45.00</cbc:Amount> <!-- BT-136, BT-141 -->
			<cbc:BaseAmount currencyID="USD">900.00</cbc:BaseAmount> <!-- BT-137, BT-142 -->
		</cac:AllowanceCharge>
		<cac:Item>
			<cbc:Name>Yashica MG2</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item1</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>9</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="USD">90.00</cbc:PriceAmount> <!-- BT-146 -->
			<cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity> <!-- BT-149, BT-150 -->
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<cbc:ID>2</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">20</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="USD">19000.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-138, BT-143 -->
			<cbc:Amount currencyID="USD">1000.00</cbc:Amount> <!-- BT-136, BT-141 -->
			<cbc:BaseAmount currencyID="USD">20000.00</cbc:BaseAmount> <!-- BT-137, BT-142 -->
		</cac:AllowanceCharge>		
		<cac:Item>
			<cbc:Name>Pentax Z-1 Body</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item2</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>9</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
			</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="USD">1000.00</cbc:PriceAmount> <!-- BT-146 -->
			<cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity> <!-- BT-149, BT-150 -->
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<cbc:ID>3</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">30</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="USD">5700.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-138, BT-143 -->
			<cbc:Amount currencyID="USD">300.00</cbc:Amount> <!-- BT-136, BT-141 -->
			<cbc:BaseAmount currencyID="USD">6000.00</cbc:BaseAmount> <!-- BT-137, BT-142 -->
		</cac:AllowanceCharge>		
		<cac:Item>
			<cbc:Name>Camera W35</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item3</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>ZR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>0</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="USD">200.00</cbc:PriceAmount> <!-- BT-146 -->
			<cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity> <!-- BT-149, BT-150 -->
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<cbc:ID>4</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">40</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="USD">8360.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-138, BT-143 -->
			<cbc:Amount currencyID="USD">440.00</cbc:Amount> <!-- BT-136, BT-141 -->
			<cbc:BaseAmount currencyID="USD">8800.00</cbc:BaseAmount> <!-- BT-137, BT-142 -->
		</cac:AllowanceCharge>		
		<cac:Item>
			<cbc:Name>Camera Prima 5</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item4</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>9</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="USD">220.00</cbc:PriceAmount> <!-- BT-146 -->
			<cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity> <!-- BT-149, BT-150 -->
		</cac:Price>
	</cac:InvoiceLine>		
</Invoice>