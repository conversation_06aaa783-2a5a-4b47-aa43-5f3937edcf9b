<?xml version="1.0" encoding="UTF-8"?>
<!--

            Copyright (C) 2020-2023 OpenPEPPOL AISBL

            Licensed under the Apache License, Version 2.0 (the "License");
            you may not use this file except in compliance with the License.
            You may obtain a copy of the License at

                    http://www.apache.org/licenses/LICENSE-2.0

            Unless required by applicable law or agreed to in writing, software
            distributed under the License is distributed on an "AS IS" BASIS,
            WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
            See the License for the specific language governing permissions and
            limitations under the License.

-->
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
  xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
  xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
	<cbc:CustomizationID>urn:peppol:pint:billing-1@my-1</cbc:CustomizationID> <!-- IBT-024 - Specification identifier -->
	<cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID> <!-- IBT-023 - Business process type -->
	<cbc:ID>***************</cbc:ID>
	<cbc:IssueDate>2023-11-30</cbc:IssueDate>
	<cbc:DueDate>2024-01-30</cbc:DueDate>
	<cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
	<cbc:TaxPointDate>2023-11-30</cbc:TaxPointDate> <!-- Move TaxPointDate here -->	
	<cbc:DocumentCurrencyCode>MYR</cbc:DocumentCurrencyCode>
	<cbc:AccountingCost>Standard</cbc:AccountingCost>
	<cbc:BuyerReference>Cik Maria</cbc:BuyerReference>	
	<cac:InvoicePeriod> <!-- IBG-14 - INVOICING PERIOD -->
		<cbc:StartDate>2023-11-01</cbc:StartDate>
		<cbc:EndDate>2023-11-30</cbc:EndDate>
	</cac:InvoicePeriod>
	<cac:OrderReference>
		<cbc:ID>PO109831</cbc:ID>
		<cbc:SalesOrderID>793</cbc:SalesOrderID>
	</cac:OrderReference>
	<cac:BillingReference>
		<cac:InvoiceDocumentReference>
			<cbc:ID>********</cbc:ID>
			<cbc:IssueDate>2023-11-30</cbc:IssueDate>
		</cac:InvoiceDocumentReference>
	</cac:BillingReference>
	<cac:DespatchDocumentReference>
		<cbc:ID>2100409</cbc:ID>
	</cac:DespatchDocumentReference>	
	<cac:ReceiptDocumentReference>
		<cbc:ID>11</cbc:ID>
	</cac:ReceiptDocumentReference>
	<cac:OriginatorDocumentReference>
		<cbc:ID>**********</cbc:ID>
	</cac:OriginatorDocumentReference>
	<cac:ContractDocumentReference>
		<cbc:ID>11</cbc:ID>
	</cac:ContractDocumentReference>

	<cac:AdditionalDocumentReference>
		<cbc:ID>Atch56890</cbc:ID>
		<cbc:DocumentDescription>Link to PDF Attchment</cbc:DocumentDescription>
		<cac:Attachment>
			<cac:ExternalReference>
				<cbc:URI>https://thelink</cbc:URI>
			</cac:ExternalReference>
		</cac:Attachment>
	</cac:AdditionalDocumentReference>
	<cac:ProjectReference>
		<cbc:ID>11</cbc:ID>
	</cac:ProjectReference>	
	<cac:AccountingSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0230">*************</cbc:EndpointID>     
			<cac:PartyIdentification>
        		<cbc:ID schemeID="0230">*************</cbc:ID>
      		</cac:PartyIdentification> 
			<cac:PartyName>
				<cbc:Name>Monitor ERP Sdn Bhd</cbc:Name>  
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>18, Jalan Excellent</cbc:StreetName>
				<cbc:AdditionalStreetName>Bandar Sunway</cbc:AdditionalStreetName>
				<cbc:CityName>Petaling Jaya</cbc:CityName>
				<cbc:PostalZone>52000</cbc:PostalZone>
				<cbc:CountrySubentity>Sunway</cbc:CountrySubentity>
				<cac:AddressLine>
					<cbc:Line>No 18</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>MY</cbc:IdentificationCode>  
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme> 
				<cbc:CompanyID>TIN20211100090911</cbc:CompanyID> <!-- IBT-032 - Seller tax registration (ibt-032) MUST occur maximum once -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyTaxScheme>
				<cbc:CompanyID>X88-1907-********</cbc:CompanyID> <!-- IBT-031 - An Invoice shall have the Supplier’s STT Registration Number -->
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Monitor ERP Sdn Bhd</cbc:RegistrationName>  
				<cbc:CompanyID>*************</cbc:CompanyID> <!-- IBT-030 - An Invoice shall have the Supplier’s Registration / Identification Number / Passport Number  -->
				<cbc:CompanyLegalForm>SENDIRIAN BERHAD</cbc:CompanyLegalForm>
			</cac:PartyLegalEntity>
			<cac:Contact>  
				<cbc:Name>Ahmad, Danial</cbc:Name>
				<cbc:Telephone>**********</cbc:Telephone>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
	</cac:AccountingSupplierParty>

	<cac:AccountingCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0230">***********</cbc:EndpointID>			
			<cac:PartyIdentification>
				<cbc:ID schemeID="0230">***********</cbc:ID>			
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Amazing HQ</cbc:Name> 
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>Menara Amazing</cbc:StreetName>
				<cbc:AdditionalStreetName>Jalan Jalan</cbc:AdditionalStreetName>
				<cbc:CityName>Petaling Jaya</cbc:CityName>
				<cbc:PostalZone>47500</cbc:PostalZone>
				<cac:AddressLine>
					<cbc:Line>20</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>MY</cbc:IdentificationCode> 
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme> <!-- IBT-048 - Buyer tax registration MUST occur maximum once.  -->
				<cbc:CompanyID>X81-0708-********</cbc:CompanyID>			
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID> 
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Amazing HQ</cbc:RegistrationName> 
				<cbc:CompanyID>***********</cbc:CompanyID> <!-- IBT-047 - An Invoice shall have the Buyer's Registration / Identification Number / Passport Number  -->
			</cac:PartyLegalEntity>
			<cac:Contact> 
				<cbc:Name>Danny Khoo</cbc:Name>
				<cbc:Telephone>**********</cbc:Telephone>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
	</cac:AccountingCustomerParty>

	<cac:PayeeParty>
		<cac:PartyIdentification>
			<cbc:ID schemeID="0230">6715595</cbc:ID>
		</cac:PartyIdentification>
		<cac:PartyName>
			<cbc:Name>Amazing HQ</cbc:Name>
		</cac:PartyName>
		<cac:PartyLegalEntity>
			<cbc:CompanyID>6715595</cbc:CompanyID>
		</cac:PartyLegalEntity>
	</cac:PayeeParty>

	<cac:TaxRepresentativeParty>
		<cac:PartyName>
			<cbc:Name>TTMY Audit</cbc:Name>
		</cac:PartyName>
		<cac:PostalAddress>
			<cbc:StreetName>Unit4.02, Menara Maybank</cbc:StreetName>
			<cbc:AdditionalStreetName>Jalan Plumbum 8</cbc:AdditionalStreetName>
			<cbc:CityName>Shah Alam</cbc:CityName>
			<cbc:PostalZone>47110</cbc:PostalZone>
			<cbc:CountrySubentity>Subentity</cbc:CountrySubentity>
			<cac:AddressLine>
				<cbc:Line>Kepayan</cbc:Line>
			</cac:AddressLine>
			<cac:Country>
				<cbc:IdentificationCode>MY</cbc:IdentificationCode>
			</cac:Country>
		</cac:PostalAddress>
		<cac:PartyTaxScheme>
			<cbc:CompanyID>MY200824324535</cbc:CompanyID>
			
			<cac:TaxScheme>
				<cbc:ID>VAT</cbc:ID>
			</cac:TaxScheme>
		</cac:PartyTaxScheme>
	</cac:TaxRepresentativeParty>

	<cac:Delivery>
		<cbc:ActualDeliveryDate>2023-11-30</cbc:ActualDeliveryDate>
		<cac:DeliveryLocation>
			<cbc:ID schemeID="0230">*************</cbc:ID>
			<cac:Address> 
				<cbc:StreetName>Jalan Amazing 88</cbc:StreetName>
				<cbc:AdditionalStreetName>Pandamaran</cbc:AdditionalStreetName>
				<cbc:CityName>Klang</cbc:CityName>
				<cbc:PostalZone>41200</cbc:PostalZone>
				<cac:AddressLine>
					<cbc:Line>Amazing Sdn Bhd</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>MY</cbc:IdentificationCode> 
				</cac:Country>
			</cac:Address>
		</cac:DeliveryLocation>
		<cac:DeliveryParty> 
			<cac:PartyName>
				<cbc:Name>Monitor ERP</cbc:Name>
			</cac:PartyName>
		</cac:DeliveryParty>
	</cac:Delivery>

	<cac:PaymentMeans>
		<cbc:PaymentMeansCode name="Credit transfer">30</cbc:PaymentMeansCode>
		<cbc:PaymentID>********</cbc:PaymentID>
		<cac:PayeeFinancialAccount>
			<cbc:ID>************</cbc:ID>
			<cbc:Name>Ahmad Danial</cbc:Name>
			<cac:FinancialInstitutionBranch>
				<cbc:ID>BIC32409</cbc:ID>
			</cac:FinancialInstitutionBranch>
		</cac:PayeeFinancialAccount>
	</cac:PaymentMeans>
	<cac:PaymentTerms>
		<cbc:Note>60 DAYS</cbc:Note>
	</cac:PaymentTerms>

	<cac:AllowanceCharge>
		<cbc:ChargeIndicator>true</cbc:ChargeIndicator> 
		<cbc:AllowanceChargeReasonCode>FC</cbc:AllowanceChargeReasonCode>
		<cbc:AllowanceChargeReason>Delivery service</cbc:AllowanceChargeReason>
		<cbc:MultiplierFactorNumeric>0</cbc:MultiplierFactorNumeric>
		<cbc:Amount currencyID="MYR">0</cbc:Amount> 
		<cbc:BaseAmount currencyID="MYR">0</cbc:BaseAmount> 
		<cac:TaxCategory>
			<cbc:ID>T</cbc:ID>
			<cbc:Percent>10</cbc:Percent>
			<cac:TaxScheme>
				<cbc:ID>VAT</cbc:ID>
			</cac:TaxScheme>
		</cac:TaxCategory>
	</cac:AllowanceCharge>

	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="MYR">1100.00</cbc:TaxAmount>
		<cac:TaxSubtotal>
			<cbc:TaxableAmount currencyID="MYR">11000.00</cbc:TaxableAmount>
			<cbc:TaxAmount currencyID="MYR">1100.00</cbc:TaxAmount>
			<cac:TaxCategory>
				<cbc:ID>T</cbc:ID>
				<cbc:Percent>10</cbc:Percent>
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID>
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
	</cac:TaxTotal>


	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="MYR">11000.00</cbc:LineExtensionAmount>
		<cbc:TaxExclusiveAmount currencyID="MYR">11000.00</cbc:TaxExclusiveAmount>
		<cbc:TaxInclusiveAmount currencyID="MYR">12100.00</cbc:TaxInclusiveAmount>
		<cbc:AllowanceTotalAmount currencyID="MYR">0</cbc:AllowanceTotalAmount>		
		<cbc:ChargeTotalAmount currencyID="MYR">0</cbc:ChargeTotalAmount>
		<cbc:PrepaidAmount currencyID="MYR">0.00</cbc:PrepaidAmount>
		<cbc:PayableRoundingAmount currencyID="MYR">0.00</cbc:PayableRoundingAmount>
		<cbc:PayableAmount  currencyID="MYR">12100.00</cbc:PayableAmount> 
	</cac:LegalMonetaryTotal>

	<cac:InvoiceLine>
		<cbc:ID>1</cbc:ID>
		<cbc:Note>Please send to gate 5</cbc:Note>
		<cbc:InvoicedQuantity unitCode="EA">100</cbc:InvoicedQuantity> <!-- [ibr-023]-An Invoice line (ibg-25) MUST have an Invoiced quantity unit of measure code (ibt-130). -->
		<cbc:LineExtensionAmount currencyID="MYR">5000.00</cbc:LineExtensionAmount>
		<cbc:AccountingCost>Marvel</cbc:AccountingCost>
		<cac:OrderLineReference>
			<cbc:LineID>10</cbc:LineID>
		</cac:OrderLineReference>
        <cac:DocumentReference>
            <cbc:ID>Mobile number **********</cbc:ID>
            <cbc:DocumentTypeCode>130</cbc:DocumentTypeCode>
        </cac:DocumentReference>
        <cac:Item>
            <cbc:Name>Marvel Chair AB18 Blue</cbc:Name> <!-- IBT-153 - Item name -->
			<cac:ClassifiedTaxCategory> <!-- IBG-30 - LINE TAX INFORMATION -->
				<cbc:ID>T</cbc:ID> <!-- IBT-151 - Invoiced item TAX category code -->
				<cbc:Percent>10</cbc:Percent> <!-- IBT-152 - Invoiced item TAX rate -->
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID> <!-- IBT-167 - Tax Scheme -->
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>               
        </cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="MYR">50.00</cbc:PriceAmount>
			<cbc:BaseQuantity unitCode="EA">1</cbc:BaseQuantity>
		</cac:Price>
	</cac:InvoiceLine>


	<cac:InvoiceLine>
		<cbc:ID>2</cbc:ID>
		<cbc:Note>Please send to Gate 6</cbc:Note>
        <cbc:InvoicedQuantity unitCode="EA">100</cbc:InvoicedQuantity> <!-- [ibr-023]-An Invoice line (ibg-25) MUST have an Invoiced quantity unit of measure code (ibt-130). -->
		<cbc:LineExtensionAmount currencyID="MYR">6000.00</cbc:LineExtensionAmount>
		<cbc:AccountingCost>Marvel</cbc:AccountingCost>
		<cac:OrderLineReference>
			<cbc:LineID>20</cbc:LineID>
		</cac:OrderLineReference>
        <cac:DocumentReference>
            <cbc:ID>Mobile number **********</cbc:ID>
            <cbc:DocumentTypeCode>130</cbc:DocumentTypeCode>
        </cac:DocumentReference>
        <cac:Item>
            <cbc:Name>Marvel Chair AB20 Red</cbc:Name> <!-- IBT-153 - Item name -->
			<cac:ClassifiedTaxCategory> <!-- IBG-30 - LINE TAX INFORMATION -->
				<cbc:ID>T</cbc:ID> <!-- IBT-151 - Invoiced item TAX category code -->
				<cbc:Percent>10</cbc:Percent> <!-- IBT-152 - Invoiced item TAX rate -->
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID> <!-- IBT-167 - Tax Scheme -->
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>   
        </cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="MYR">60.00</cbc:PriceAmount>
			<cbc:BaseQuantity unitCode="EA">1</cbc:BaseQuantity>
		</cac:Price>
	</cac:InvoiceLine>


</Invoice>
