<?xml version="1.0" encoding="UTF-8"?>
<EndUserStatisticsReport xmlns="urn:fdc:peppol:end-user-statistics-report:1.1">
  <CustomizationID>urn:fdc:peppol.eu:edec:trns:end-user-statistics-report:1.1</CustomizationID>
  <ProfileID>urn:fdc:peppol.eu:edec:bis:reporting:1.0</ProfileID>
  <Header>
    <ReportPeriod>
      <StartDate>2023-07-01</StartDate>
      <EndDate>2023-07-31</EndDate>
    </ReportPeriod>
    <ReporterID schemeID="CertSubjectCN">PDE000001</ReporterID>
  </Header>
  <FullSet>
    <SendingEndUsers>6</SendingEndUsers>
    <ReceivingEndUsers>2</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>6</SendingOrReceivingEndUsers>
  </FullSet>
  <Subset type="PerDT-PR">
    <Key metaSchemeID="DT" schemeID="busdox-docid-qns">urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</Key>
    <Key metaSchemeID="PR" schemeID="cenbii-procid-ubl">urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</Key>
    <SendingEndUsers>4</SendingEndUsers>
    <ReceivingEndUsers>2</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>6</SendingOrReceivingEndUsers>
  </Subset>
  <Subset type="PerDT-PR">
    <Key metaSchemeID="DT" schemeID="busdox-docid-qns">urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</Key>
    <Key metaSchemeID="PR" schemeID="cenbii-procid-ubl">urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</Key>
    <SendingEndUsers>6</SendingEndUsers>
    <ReceivingEndUsers>0</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>6</SendingOrReceivingEndUsers>
  </Subset>
  <Subset type="PerDT-PR-EUC">
    <Key metaSchemeID="DT" schemeID="busdox-docid-qns">urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</Key>
    <Key metaSchemeID="PR" schemeID="cenbii-procid-ubl">urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</Key>
    <Key metaSchemeID="CC" schemeID="EndUserCountry">JP</Key>
    <SendingEndUsers>0</SendingEndUsers>
    <ReceivingEndUsers>2</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>2</SendingOrReceivingEndUsers>
  </Subset>
  <Subset type="PerDT-EUC">
    <Key metaSchemeID="DT" schemeID="busdox-docid-qns">urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</Key>
    <Key metaSchemeID="CC" schemeID="EndUserCountry">JP</Key>
    <SendingEndUsers>0</SendingEndUsers>
    <ReceivingEndUsers>2</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>2</SendingOrReceivingEndUsers>
  </Subset>
  <Subset type="PerDT-EUC">
    <Key metaSchemeID="DT" schemeID="busdox-docid-qns">urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2::CreditNote##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</Key>
    <Key metaSchemeID="CC" schemeID="EndUserCountry">NO</Key>
    <SendingEndUsers>4</SendingEndUsers>
    <ReceivingEndUsers>0</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>4</SendingOrReceivingEndUsers>
  </Subset>
  <Subset type="PerDT-EUC">
    <Key metaSchemeID="DT" schemeID="busdox-docid-qns">urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</Key>
    <Key metaSchemeID="CC" schemeID="EndUserCountry">FI</Key>
    <SendingEndUsers>5</SendingEndUsers>
    <ReceivingEndUsers>0</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>5</SendingOrReceivingEndUsers>
  </Subset>
  <Subset type="PerDT-EUC">
    <Key metaSchemeID="DT" schemeID="busdox-docid-qns">urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</Key>
    <Key metaSchemeID="CC" schemeID="EndUserCountry">NO</Key>
    <SendingEndUsers>3</SendingEndUsers>
    <ReceivingEndUsers>0</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>3</SendingOrReceivingEndUsers>
  </Subset>
  <Subset type="PerEUC">
    <Key metaSchemeID="CC" schemeID="EndUserCountry">FI</Key>
    <SendingEndUsers>5</SendingEndUsers>
    <ReceivingEndUsers>0</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>5</SendingOrReceivingEndUsers>
  </Subset>
  <Subset type="PerEUC">
    <Key metaSchemeID="CC" schemeID="EndUserCountry">JP</Key>
    <SendingEndUsers>0</SendingEndUsers>
    <ReceivingEndUsers>2</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>2</SendingOrReceivingEndUsers>
  </Subset>
  <Subset type="PerEUC">
    <Key metaSchemeID="CC" schemeID="EndUserCountry">NO</Key>
    <SendingEndUsers>6</SendingEndUsers>
    <ReceivingEndUsers>0</ReceivingEndUsers>
    <SendingOrReceivingEndUsers>6</SendingOrReceivingEndUsers>
  </Subset>
</EndUserStatisticsReport>
