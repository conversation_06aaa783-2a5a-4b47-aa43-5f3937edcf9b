<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
    xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#conformant#urn:fdc:peppol.eu:2017:poacc:billing:international:aunz:3.0</cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>Invoice number 114</cbc:ID>
    <cbc:IssueDate>2019-10-28</cbc:IssueDate>
    <cbc:DueDate>2019-11-30</cbc:DueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
	<cbc:Note>Adjustment note to accompany previous invoice</cbc:Note>
    <cbc:DocumentCurrencyCode>AUD</cbc:DocumentCurrencyCode>
    <cbc:AccountingCost>4025:123:4343</cbc:AccountingCost>
    <cbc:BuyerReference>Your reference 123</cbc:BuyerReference>
    <cac:InvoicePeriod>
       <cbc:StartDate>2019-10-28</cbc:StartDate>
       <cbc:EndDate>2019-11-30</cbc:EndDate>
       <cbc:DescriptionCode>432</cbc:DescriptionCode>
    </cac:InvoicePeriod>
	<cac:OrderReference>
       <cbc:ID>PurchaseOrderReference</cbc:ID>
		<cbc:SalesOrderID>********</cbc:SalesOrderID>
    </cac:OrderReference>
    <cac:BillingReference>
       <cac:InvoiceDocumentReference>
           <cbc:ID>********</cbc:ID>
           <cbc:IssueDate>2019-10-27</cbc:IssueDate>
       </cac:InvoiceDocumentReference>
    </cac:BillingReference>

    <cac:AccountingSupplierParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0151">***********</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID>***********</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>Supplier ABC Pty Ltd</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>52 Rosewood St</cbc:StreetName>
                <cbc:CityName>Sydney</cbc:CityName>
                <cbc:PostalZone>2758</cbc:PostalZone>
				<cbc:CountrySubentity>NSW</cbc:CountrySubentity>
                <cac:Country>
                    <cbc:IdentificationCode>AU</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>***********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>Supplier ABC Pty Ltd</cbc:RegistrationName>
                <cbc:CompanyID  schemeID="0151">***********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>Supplier contact</cbc:Name>
                <cbc:Telephone>Mobile **********</cbc:Telephone>
                <cbc:ElectronicMail>admin@ABC_Ltd.com.au</cbc:ElectronicMail>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingSupplierParty>

    <cac:AccountingCustomerParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0151">***********</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID schemeID="0151">***********</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>Buyer CDE Pty Ltd</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Building 123, EAST RD</cbc:StreetName>
                <cbc:CityName>Sydney</cbc:CityName>
                <cbc:PostalZone>2310</cbc:PostalZone>
				<cbc:CountrySubentity>NSW</cbc:CountrySubentity>
                <cac:Country>
                    <cbc:IdentificationCode>AU</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>***********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>Buyer CDE Pty Ltd</cbc:RegistrationName>
                <cbc:CompanyID schemeID="0151">***********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>Accounts Payable</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>
	
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode name="Credit transfer">30</cbc:PaymentMeansCode>
        <cbc:PaymentID>PaymentReferenceText</cbc:PaymentID>
        <cac:PayeeFinancialAccount>
            <cbc:ID>AccountNumber</cbc:ID>
            <cbc:Name>AccountName</cbc:Name>
            <cac:FinancialInstitutionBranch>
                <cbc:ID>BSB Number</cbc:ID>
            </cac:FinancialInstitutionBranch>
        </cac:PayeeFinancialAccount>  
    </cac:PaymentMeans>
	
    
	<cac:TaxTotal>
        <cbc:TaxAmount currencyID="AUD">117.72</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="AUD">1177.20</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="AUD">117.72</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>10</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
		<cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="AUD">-1177.20</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="AUD">0.00</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>E</cbc:ID>
                <cbc:Percent>0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>


    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="AUD">0.00</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="AUD">0.00</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="AUD">117.72</cbc:TaxInclusiveAmount>
        <cbc:ChargeTotalAmount currencyID="AUD">0.00</cbc:ChargeTotalAmount>
        <cbc:PrepaidAmount currencyID="AUD">0.00</cbc:PrepaidAmount>
        <cbc:PayableAmount currencyID="AUD">117.72</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
 

    <cac:InvoiceLine>
       <cbc:ID>1</cbc:ID>
       <cbc:InvoicedQuantity unitCode="1I">-1</cbc:InvoicedQuantity>
       <cbc:LineExtensionAmount currencyID= "AUD">-1177.20</cbc:LineExtensionAmount>
           <cbc:AccountingCost>Accounting Cost</cbc:AccountingCost>
           <cac:InvoicePeriod>
           <cbc:StartDate>2019-10-28</cbc:StartDate> 
           <cbc:EndDate>2019-11-30</cbc:EndDate> 
       </cac:InvoicePeriod>
       <cac:OrderLineReference>
            <cbc:LineID>123</cbc:LineID>
       </cac:OrderLineReference>
		<cac:Item>
			<cbc:Description>Product</cbc:Description>
			<cbc:Name>Name of product</cbc:Name>
			<cac:OriginCountry>
				<cbc:IdentificationCode>AU</cbc:IdentificationCode>
			</cac:OriginCountry>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>E</cbc:ID>
				<cbc:Percent>0</cbc:Percent>
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
        </cac:Item>
       <cac:Price>
           <cbc:PriceAmount currencyID="AUD">1177.20</cbc:PriceAmount>
       </cac:Price>
    </cac:InvoiceLine>

	 <cac:InvoiceLine>
       <cbc:ID>2</cbc:ID>
       <cbc:InvoicedQuantity unitCode="1I">1</cbc:InvoicedQuantity>
       <cbc:LineExtensionAmount currencyID= "AUD">1177.20</cbc:LineExtensionAmount>
           <cbc:AccountingCost>Accounting Cost</cbc:AccountingCost>
           <cac:InvoicePeriod>
           <cbc:StartDate>2019-10-28</cbc:StartDate> 
           <cbc:EndDate>2019-11-30</cbc:EndDate> 
       </cac:InvoicePeriod>
       <cac:OrderLineReference>
            <cbc:LineID>123</cbc:LineID>
       </cac:OrderLineReference>
		<cac:Item>
			<cbc:Description>Product</cbc:Description>
			<cbc:Name>Namoe of product</cbc:Name>
			<cac:OriginCountry>
				<cbc:IdentificationCode>AU</cbc:IdentificationCode>
			</cac:OriginCountry>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>S</cbc:ID>
				<cbc:Percent>10</cbc:Percent>
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
        </cac:Item>
       <cac:Price>
           <cbc:PriceAmount currencyID="AUD">1177.20</cbc:PriceAmount>
       </cac:Price>
    </cac:InvoiceLine>



   </Invoice>