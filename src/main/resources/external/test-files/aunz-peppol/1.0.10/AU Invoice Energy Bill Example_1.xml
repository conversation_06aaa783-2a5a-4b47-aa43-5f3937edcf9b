<?xml version="1.0" encoding="UTF-8"?>
<!-- Example of a simple invoice with 'mixed' taxable and non-taxable supplies including a non-taxable solar rebate (e.g. micro-business not registered for GST) -->
<Invoice xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">
	<cbc:CustomizationID>urn:cen.eu:en16931:2017#conformant#urn:fdc:peppol.eu:2017:poacc:billing:international:aunz:3.0</cbc:CustomizationID>
	<cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
	<cbc:ID>Invoice01</cbc:ID>
	<cbc:IssueDate>2022-07-29</cbc:IssueDate>
	<cbc:DueDate>2022-08-30</cbc:DueDate>
	<cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
	<cbc:Note>Tax invoice. Please note you have $384.24 OVERDUE from prior bills.</cbc:Note> <!-- Free text field can bring attention to prior unpaid amount etc. -->
	<cbc:DocumentCurrencyCode>AUD</cbc:DocumentCurrencyCode>
	<cbc:BuyerReference>Simple solar plan</cbc:BuyerReference> <!-- Purchase Order and/or Buyer Reference MUST be provided -->
	<cac:InvoicePeriod>
		<!-- Period is optional at the invoice and line levels -->
		<cbc:StartDate>2022-06-15</cbc:StartDate>
		<cbc:EndDate>2022-07-15</cbc:EndDate>
	</cac:InvoicePeriod>
	<cac:AdditionalDocumentReference>
		<!-- Multiple attachments and external links may optionally be included -->
		<cbc:ID>Invoice01.pdf</cbc:ID>
		<cac:Attachment>
			<!-- For brevity, this sample Attachment is not representative of an embedded pdf -->
			<cbc:EmbeddedDocumentBinaryObject mimeCode="application/pdf" filename="Invoice01.pdf">UGxhaW4gdGV4dCBpbiBwbGFjZSBvZiBwZGYgYXR0YWNobWVudCBmb3Igc2FtcGxlIGludm9pY2Vz</cbc:EmbeddedDocumentBinaryObject>
		</cac:Attachment>
	</cac:AdditionalDocumentReference>
	<cac:AccountingSupplierParty>
		<!-- Seller details -->
		<cac:Party>
			<cbc:EndpointID schemeID="0151">***********</cbc:EndpointID> <!-- Seller 'Peppol ID' -->
			<cac:PostalAddress>
				<cbc:CityName>Harrison</cbc:CityName>
				<cbc:PostalZone>2912</cbc:PostalZone>
				<cbc:CountrySubentity>NSW</cbc:CountrySubentity>
				<cac:Country>
					<cbc:IdentificationCode>AU</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Grey Roo Energy</cbc:RegistrationName>
				<cbc:CompanyID schemeID="0151">***********</cbc:CompanyID> <!-- Seller ABN -->
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingSupplierParty>
	<cac:AccountingCustomerParty>
		<!-- Buyer/customer details -->
		<cac:Party>
			<cbc:EndpointID schemeID="0151">***********</cbc:EndpointID> <!-- Buyer/customer 'Peppol ID' -->
			<cac:PartyIdentification>
				<cbc:ID>AccountNumber123</cbc:ID> <!-- Buyer/customer account number, assigned by the supplier -->
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>100 Queen Street</cbc:StreetName>
				<cbc:CityName>Sydney</cbc:CityName>
				<cbc:PostalZone>2000</cbc:PostalZone>
				<cbc:CountrySubentity>NSW</cbc:CountrySubentity>
				<cac:Country>
					<cbc:IdentificationCode>AU</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Trotters Incorporated</cbc:RegistrationName>
				<cbc:CompanyID schemeID="0151">***********</cbc:CompanyID> <!-- Buyer/customer ABN -->
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:Name>Lisa Johnson</cbc:Name>
			</cac:Contact>
		</cac:Party>
	</cac:AccountingCustomerParty>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="AUD">15.94</cbc:TaxAmount>
		<cac:TaxSubtotal>
			<!-- Subtotal for 'S' Standard-rated tax category of 10% GST -->
			<cbc:TaxableAmount currencyID="AUD">159.43</cbc:TaxableAmount>
			<cbc:TaxAmount currencyID="AUD">15.94</cbc:TaxAmount>
			<cac:TaxCategory>
				<cbc:ID>S</cbc:ID>
				<cbc:Percent>10</cbc:Percent>
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
		<cac:TaxSubtotal>
			<!-- Subtotal for 'Z' Zero-rated tax category of 0% GST -->
			<cbc:TaxableAmount currencyID="AUD">-13.5</cbc:TaxableAmount>
			<cbc:TaxAmount currencyID="AUD">0.00</cbc:TaxAmount>
			<cac:TaxCategory>
				<cbc:ID>Z</cbc:ID>
				<cbc:Percent>0</cbc:Percent>
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
	</cac:TaxTotal>
	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="AUD">145.93</cbc:LineExtensionAmount>
		<cbc:TaxExclusiveAmount currencyID="AUD">145.93</cbc:TaxExclusiveAmount>
		<cbc:TaxInclusiveAmount currencyID="AUD">161.87</cbc:TaxInclusiveAmount>
		<cbc:PayableAmount currencyID="AUD">161.87</cbc:PayableAmount> <!-- New charges invoiced (excluding prior unpaid amount) -->
	</cac:LegalMonetaryTotal>
	<cac:InvoiceLine>
		<!-- Line with 10% GST -->
		<cbc:ID>1</cbc:ID>
		<cbc:InvoicedQuantity unitCode="KWH">325.2</cbc:InvoicedQuantity>
		<cbc:LineExtensionAmount currencyID="AUD">129.04</cbc:LineExtensionAmount>
		<cac:Item>
			<cbc:Name>Electricity charges - all day rate NMI 9000074677</cbc:Name>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>S</cbc:ID>
				<cbc:Percent>10</cbc:Percent> <!-- 10% GST -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="AUD">0.3968</cbc:PriceAmount>
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<!-- Line with credit value and zero GST -->
		<cbc:ID>2</cbc:ID>
		<cbc:InvoicedQuantity unitCode="KWH">-150</cbc:InvoicedQuantity>
		<cbc:LineExtensionAmount currencyID="AUD">-13.5</cbc:LineExtensionAmount>
		<cac:Item>
			<cbc:Name>Solar feed-in rebate NMI 9000074677</cbc:Name>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>Z</cbc:ID>
				<cbc:Percent>0</cbc:Percent> <!-- 0% GST -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="AUD">0.09</cbc:PriceAmount>
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<!-- Line with 10% GST -->
		<cbc:ID>3</cbc:ID>
		<cbc:InvoicedQuantity unitCode="DAY">31</cbc:InvoicedQuantity>
		<cbc:LineExtensionAmount currencyID="AUD">30.39</cbc:LineExtensionAmount>
		<cac:Item>
			<cbc:Name>Supply charge</cbc:Name>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>S</cbc:ID>
				<cbc:Percent>10</cbc:Percent> <!-- 10% GST -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="AUD">0.9803</cbc:PriceAmount>
		</cac:Price>
	</cac:InvoiceLine>
</Invoice>
