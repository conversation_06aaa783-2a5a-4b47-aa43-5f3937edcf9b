<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
    xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#conformant#urn:fdc:peppol.eu:2017:poacc:billing:international:aunz:3.0</cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>Snippet1</cbc:ID>
    <cbc:IssueDate>2019-07-29</cbc:IssueDate>
    <cbc:DueDate>2019-08-30</cbc:DueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
    <cbc:Note>Some Blurb about the Invoice</cbc:Note>
    <cbc:DocumentCurrencyCode>NZD</cbc:DocumentCurrencyCode>
    <cbc:AccountingCost>4025:123:4343</cbc:AccountingCost>
    <cbc:BuyerReference>0150abc</cbc:BuyerReference>
    <!--cac:InvoicePeriod>
       <cbc:StartDate>2019-06-01</cbc:StartDate>
       <cbc:EndDate>2019-07-30</cbc:EndDate>
       <cbc:DescriptionCode>432</cbc:DescriptionCode>
    </cac:InvoicePeriod-->
       <cac:OrderReference>
       <cbc:ID>SOMEBLERB</cbc:ID>
    <cbc:SalesOrderID>********</cbc:SalesOrderID>
    </cac:OrderReference>
    <cac:BillingReference>
       <cac:InvoiceDocumentReference>
           <cbc:ID>THEIDGOESHERE</cbc:ID>
           <cbc:IssueDate>2019-07-30</cbc:IssueDate>
       </cac:InvoiceDocumentReference>
    </cac:BillingReference>
    <cac:DespatchDocumentReference>
       <cbc:ID>DDR-REF</cbc:ID>
    </cac:DespatchDocumentReference>
    <cac:ReceiptDocumentReference>
       <cbc:ID>RD-REF</cbc:ID>
    </cac:ReceiptDocumentReference>
    <cac:OriginatorDocumentReference>
       <cbc:ID>OD-REF</cbc:ID>
    </cac:OriginatorDocumentReference>
    <cac:ContractDocumentReference>
       <cbc:ID>CD-REF</cbc:ID>
    </cac:ContractDocumentReference>
    <cac:ProjectReference>
       <cbc:ID>PR-REF</cbc:ID>
    </cac:ProjectReference>
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0088">*************</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID>*************</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>SupplierTradingName Ltd.</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Main street 1</cbc:StreetName>
                <cbc:AdditionalStreetName>Postbox 123</cbc:AdditionalStreetName>
                <cbc:CityName>Wellington</cbc:CityName>
                <cbc:PostalZone>NZ 123 EW</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>NZ</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>***********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>SupplierOfficialName Ltd</cbc:RegistrationName>
                <cbc:CompanyID  schemeID="0088">*************</cbc:CompanyID>
                <cbc:CompanyLegalForm>Partnership</cbc:CompanyLegalForm>
            </cac:PartyLegalEntity>

            <cac:Contact>
                <cbc:Name>Ronald MacDonald</cbc:Name>
                <cbc:Telephone>Mobile 021 1090666</cbc:Telephone>
                <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingSupplierParty>

    <cac:AccountingCustomerParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0088">*************</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID schemeID="0088">*************</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>Trotters Trading Co Ltd</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>100 Queen Street</cbc:StreetName>
                <cbc:AdditionalStreetName>Po box 878</cbc:AdditionalStreetName>
                <cbc:CityName>Auckland</cbc:CityName>
                <cbc:PostalZone>A36577</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>NZ</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>***********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>Buyer Official Name</cbc:RegistrationName>
                <cbc:CompanyID schemeID="0088">*************</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>Lisa Johnson</cbc:Name>
                <cbc:Telephone>********</cbc:Telephone>
                <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>

    <cac:PayeeParty>
       <cac:PartyIdentification>
           <cbc:ID>schemeID="SEPA" SR678659898009</cbc:ID>
       </cac:PartyIdentification>
       <cac:PartyName>
           <cbc:Name>Mr Anderson</cbc:Name>
       </cac:PartyName>

       <cac:PartyLegalEntity>
           <cbc:CompanyID schemeID="0088">*************</cbc:CompanyID>
       </cac:PartyLegalEntity>    
    </cac:PayeeParty>

    <cac:TaxRepresentativeParty>
       <cac:PartyName>
           <cbc:Name>Mr Wilson</cbc:Name>
       </cac:PartyName>
       <cac:PostalAddress>
           <cbc:StreetName>16 Stout Street</cbc:StreetName>
           <cbc:AdditionalStreetName>Po box 878</cbc:AdditionalStreetName>
           <cbc:CityName>Wellington</cbc:CityName>
           <cbc:PostalZone>1111</cbc:PostalZone>
           <cbc:CountrySubentity>Kapiti Coast</cbc:CountrySubentity>
           <cac:AddressLine>
               <cbc:Line>Out-in-theSticks</cbc:Line>
           </cac:AddressLine>
           <cac:Country>
                    <cbc:IdentificationCode>NZ</cbc:IdentificationCode>
           </cac:Country>
      </cac:PostalAddress>
           <cac:PartyTaxScheme>
                <cbc:CompanyID>***********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme> 
    </cac:TaxRepresentativeParty>


    <cac:Delivery>
        <cbc:ActualDeliveryDate>2019-06-01</cbc:ActualDeliveryDate>
        <cac:DeliveryLocation>
            <cbc:ID schemeID="0088">*************</cbc:ID>
            <cac:Address>
                <cbc:StreetName>Delivery street 2</cbc:StreetName>
                <cbc:AdditionalStreetName>Building 56</cbc:AdditionalStreetName>
                <cbc:CityName>Auckland</cbc:CityName>
                <cbc:PostalZone>21234</cbc:PostalZone>
                <cbc:CountrySubentity>Northland</cbc:CountrySubentity>
                <cac:AddressLine>
                    <cbc:Line>One-Tree-Hill</cbc:Line>
                </cac:AddressLine>
                <cac:Country>
                    <cbc:IdentificationCode>NZ</cbc:IdentificationCode>
                </cac:Country>
            </cac:Address>
        </cac:DeliveryLocation>
        <cac:DeliveryParty>
            <cac:PartyName>
                <cbc:Name>Delivery party Name</cbc:Name>
            </cac:PartyName>
        </cac:DeliveryParty>
    </cac:Delivery>
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode name="Credit transfer">30</cbc:PaymentMeansCode>
        <cbc:PaymentID>Snippet1</cbc:PaymentID>
        <cac:PayeeFinancialAccount>
            <cbc:ID>IBAN32423940</cbc:ID>
            <cbc:Name>AccountName</cbc:Name>
            <cac:FinancialInstitutionBranch>
                <cbc:ID>BIC324098</cbc:ID>
            </cac:FinancialInstitutionBranch>
        </cac:PayeeFinancialAccount>
        <cac:PaymentMandate>
            <cbc:ID>SEPA3245543940</cbc:ID>
            <cac:PayerFinancialAccount>
                <cbc:ID>BIC32778</cbc:ID>
            </cac:PayerFinancialAccount>
       </cac:PaymentMandate>
    </cac:PaymentMeans>
    <cac:PaymentTerms>
        <cbc:Note>Payment within 30 days</cbc:Note>
    </cac:PaymentTerms>

    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="NZD">581.20</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="NZD">3874.65</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="NZD">581.20</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>15</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>



    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="NZD">3874.65</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="NZD">3874.65</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="NZD">4455.85</cbc:TaxInclusiveAmount>
        <cbc:AllowanceTotalAmount currencyID="NZD">0.00</cbc:AllowanceTotalAmount>
        <cbc:PrepaidAmount currencyID="NZD">0.00</cbc:PrepaidAmount>
        <cbc:PayableAmount currencyID="NZD">4455.85</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
 





    <cac:InvoiceLine>
       <cbc:ID>1</cbc:ID>
       <cbc:Note>Some Blurb Giving More Info about the Invoice Line</cbc:Note>
       <cbc:InvoicedQuantity unitCode="E99">10</cbc:InvoicedQuantity>
       <cbc:LineExtensionAmount currencyID= "NZD">599.90</cbc:LineExtensionAmount>
           <cbc:AccountingCost>Consulting Fees</cbc:AccountingCost>
           <cac:InvoicePeriod>
           <cbc:StartDate>2019-06-01</cbc:StartDate> 
           <cbc:EndDate>2019-07-30</cbc:EndDate> 
       </cac:InvoicePeriod>
       <cac:OrderLineReference>
            <cbc:LineID>123</cbc:LineID>
       </cac:OrderLineReference>
       <cac:DocumentReference>
            <cbc:ID schemeID="HWB">**********</cbc:ID>
            <cbc:DocumentTypeCode>130</cbc:DocumentTypeCode> 
       </cac:DocumentReference>


    <cac:Item>
        <cbc:Description>Widgets True and Fair</cbc:Description>
           <cbc:Name>True-Widgets</cbc:Name>
           <cac:BuyersItemIdentification>
              <cbc:ID>W659590</cbc:ID>
           </cac:BuyersItemIdentification>
           <cac:SellersItemIdentification>
              <cbc:ID>WG546767</cbc:ID>
           </cac:SellersItemIdentification>
           <cac:StandardItemIdentification>
              <cbc:ID  schemeID="0088">WG546767</cbc:ID>
           </cac:StandardItemIdentification>
            <cac:OriginCountry>
                <cbc:IdentificationCode>NZ</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="SRV">09348023</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>15</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>

       <cac:Price>
           <cbc:PriceAmount currencyID="NZD">59.99</cbc:PriceAmount>
           <cac:AllowanceCharge>
              <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
              <cbc:Amount currencyID="NZD">10.00</cbc:Amount>
              <cbc:BaseAmount currencyID="NZD">69.99</cbc:BaseAmount>
           </cac:AllowanceCharge>
       </cac:Price>

    </cac:InvoiceLine>







   <cac:InvoiceLine>
      <cbc:ID>2</cbc:ID>
      <cbc:InvoicedQuantity unitCode="DAY">2</cbc:InvoicedQuantity>
      <cbc:LineExtensionAmount currencyID="NZD">1400</cbc:LineExtensionAmount>
      <cac:OrderLineReference>
          <cbc:LineID>123</cbc:LineID>
      </cac:OrderLineReference>
      <cac:Item>
          <cbc:Description>Description 2</cbc:Description>
          <cbc:Name>item name 2</cbc:Name>
          <cac:StandardItemIdentification>
              <cbc:ID schemeID="0088">21382183120983</cbc:ID>
          </cac:StandardItemIdentification>
          <cac:OriginCountry>
              <cbc:IdentificationCode>NO</cbc:IdentificationCode>
          </cac:OriginCountry>
          <cac:CommodityClassification>
              <cbc:ItemClassificationCode listID="SRV">09348023</cbc:ItemClassificationCode>
          </cac:CommodityClassification>
          <cac:ClassifiedTaxCategory>
              <cbc:ID>S</cbc:ID>
              <cbc:Percent>15</cbc:Percent>
              <cac:TaxScheme>
                  <cbc:ID>GST</cbc:ID>
              </cac:TaxScheme>
          </cac:ClassifiedTaxCategory>
      </cac:Item>
          <cac:Price>
           <cbc:PriceAmount currencyID="NZD">700</cbc:PriceAmount>
           <cac:AllowanceCharge>
              <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
              <cbc:Amount currencyID="NZD">50.00</cbc:Amount>
              <cbc:BaseAmount currencyID="NZD">750</cbc:BaseAmount>
           </cac:AllowanceCharge>
       </cac:Price>
   </cac:InvoiceLine>




<cac:InvoiceLine>
       <cbc:ID>3</cbc:ID>
       <cbc:Note>Invoice Line Description</cbc:Note>
       <cbc:InvoicedQuantity unitCode="M66">25</cbc:InvoicedQuantity>
       <cbc:LineExtensionAmount currencyID= "NZD">1874.75</cbc:LineExtensionAmount>
           <cbc:AccountingCost>Consulting Fees</cbc:AccountingCost>
           <cac:InvoicePeriod>
           <cbc:StartDate>2019-06-01</cbc:StartDate> 
           <cbc:EndDate>2019-07-30</cbc:EndDate> 
       </cac:InvoicePeriod>
       <cac:OrderLineReference>
            <cbc:LineID>123</cbc:LineID>
       </cac:OrderLineReference>
       <cac:DocumentReference>
            <cbc:ID schemeID="HWB">**********</cbc:ID>
            <cbc:DocumentTypeCode>130</cbc:DocumentTypeCode> 
       </cac:DocumentReference>

    <cac:Item>
        <cbc:Description>Widgets True and Fair</cbc:Description>
           <cbc:Name>True-Widgets</cbc:Name>
           <cac:BuyersItemIdentification>
              <cbc:ID>W659590</cbc:ID>
           </cac:BuyersItemIdentification>
           <cac:SellersItemIdentification>
              <cbc:ID>WG546767</cbc:ID>
           </cac:SellersItemIdentification>
           <cac:StandardItemIdentification>
              <cbc:ID  schemeID="0088">WG546767</cbc:ID>
           </cac:StandardItemIdentification>
            <cac:OriginCountry>
                <cbc:IdentificationCode>NZ</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="SRV">09348023</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>15</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>GST</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>

       <cac:Price>
           <cbc:PriceAmount currencyID="NZD">74.99</cbc:PriceAmount>
           <cac:AllowanceCharge>
              <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
              <cbc:Amount currencyID="NZD">25.00</cbc:Amount>
              <cbc:BaseAmount currencyID="NZD">99.99</cbc:BaseAmount>
           </cac:AllowanceCharge>
       </cac:Price>

    </cac:InvoiceLine>




</Invoice>
