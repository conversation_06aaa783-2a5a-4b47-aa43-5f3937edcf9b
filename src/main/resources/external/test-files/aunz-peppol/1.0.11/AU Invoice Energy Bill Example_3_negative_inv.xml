<?xml version="1.0" encoding="UTF-8"?>
<!-- Example of a simple negative invoice -->
<Invoice xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">
	<cbc:CustomizationID>urn:cen.eu:en16931:2017#conformant#urn:fdc:peppol.eu:2017:poacc:billing:international:aunz:3.0</cbc:CustomizationID>
	<cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
	<cbc:ID>Invoice03</cbc:ID>
	<cbc:IssueDate>2022-07-31</cbc:IssueDate>
	<cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
	<cbc:Note>Adjustment note to reverse prior bill Invoice01.</cbc:Note><!-- Free text field can bring attention to reason for credit etc. -->
	<cbc:DocumentCurrencyCode>AUD</cbc:DocumentCurrencyCode>
	<cbc:BuyerReference>Simple solar plan</cbc:BuyerReference>
	<cac:InvoicePeriod>
		<!-- Period is optional at the invoice and line levels -->
		<cbc:StartDate>2022-06-15</cbc:StartDate>
		<cbc:EndDate>2022-07-15</cbc:EndDate>
	</cac:InvoicePeriod>
	<cac:BillingReference>
		<cac:InvoiceDocumentReference>
			<cbc:ID>Invoice01</cbc:ID>
			<cbc:IssueDate>2022-07-29</cbc:IssueDate>
		</cac:InvoiceDocumentReference>
	</cac:BillingReference>
	<cac:AdditionalDocumentReference>
		<!-- Multiple attachments and external links may optionally be included -->
		<cbc:ID>Invoice03.pdf</cbc:ID>
		<cac:Attachment>
			<!-- For brevity, this sample Attachment is not representative of an embedded pdf -->
			<cbc:EmbeddedDocumentBinaryObject mimeCode="application/pdf" filename="Invoice03.pdf">UGxhaW4gdGV4dCBpbiBwbGFjZSBvZiBwZGYgYXR0YWNobWVudCBmb3Igc2FtcGxlIGludm9pY2Vz</cbc:EmbeddedDocumentBinaryObject>
		</cac:Attachment>
	</cac:AdditionalDocumentReference>
	<cac:AccountingSupplierParty>
		<!-- Seller details -->
		<cac:Party>
			<cbc:EndpointID schemeID="0151">***********</cbc:EndpointID><!-- Seller 'Peppol ID' -->
			<cac:PostalAddress>
				<cbc:CityName>Harrison</cbc:CityName>
				<cbc:PostalZone>2912</cbc:PostalZone>
				<cbc:CountrySubentity>NSW</cbc:CountrySubentity>
				<cac:Country>
					<cbc:IdentificationCode>AU</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Grey Roo Energy</cbc:RegistrationName>
				<cbc:CompanyID schemeID="0151">***********</cbc:CompanyID><!-- Seller ABN -->
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingSupplierParty>
	<cac:AccountingCustomerParty>
		<!-- Buyer/customer details -->
		<cac:Party>
			<cbc:EndpointID schemeID="0151">***********</cbc:EndpointID><!-- Buyer/customer 'Peppol ID' -->
			<cac:PartyIdentification>
				<cbc:ID>AccountNumber123</cbc:ID><!-- Buyer/customer account number, assigned by the supplier -->
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>100 Queen Street</cbc:StreetName>
				<cbc:CityName>Sydney</cbc:CityName>
				<cbc:PostalZone>2000</cbc:PostalZone>
				<cbc:CountrySubentity>NSW</cbc:CountrySubentity>
				<cac:Country>
					<cbc:IdentificationCode>AU</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Trotters Incorporated</cbc:RegistrationName>
				<cbc:CompanyID schemeID="0151">***********</cbc:CompanyID><!-- Buyer/customer ABN -->
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:Name>Lisa Johnson</cbc:Name>
			</cac:Contact>
		</cac:Party>
	</cac:AccountingCustomerParty>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="AUD">-15.94</cbc:TaxAmount>
		<cac:TaxSubtotal>
			<!-- Subtotal for 'S' Standard-rated tax category of 10% GST -->
			<cbc:TaxableAmount currencyID="AUD">-159.43</cbc:TaxableAmount>
			<cbc:TaxAmount currencyID="AUD">-15.94</cbc:TaxAmount>
			<cac:TaxCategory>
				<cbc:ID>S</cbc:ID>
				<cbc:Percent>10</cbc:Percent>
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
	</cac:TaxTotal>
	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="AUD">-159.43</cbc:LineExtensionAmount>
		<cbc:TaxExclusiveAmount currencyID="AUD">-159.43</cbc:TaxExclusiveAmount>
		<cbc:TaxInclusiveAmount currencyID="AUD">-175.37</cbc:TaxInclusiveAmount>
		<cbc:PayableAmount currencyID="AUD">-175.37</cbc:PayableAmount><!-- Credited to account -->
	</cac:LegalMonetaryTotal>
	<cac:InvoiceLine>
		<!-- Line with 10% GST -->
		<cbc:ID>1</cbc:ID>
		<cbc:InvoicedQuantity unitCode="KWH">-325.2</cbc:InvoicedQuantity>
		<cbc:LineExtensionAmount currencyID="AUD">-129.04</cbc:LineExtensionAmount>
		<cac:Item>
			<cbc:Name>Adjustment - reverse prior Electricity charges - all day rate NMI **********</cbc:Name>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>S</cbc:ID>
				<cbc:Percent>10</cbc:Percent><!-- 10% GST -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="AUD">0.3968</cbc:PriceAmount>
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<!-- Line with 10% GST -->
		<cbc:ID>2</cbc:ID>
		<cbc:InvoicedQuantity unitCode="DAY">-31</cbc:InvoicedQuantity>
		<cbc:LineExtensionAmount currencyID="AUD">-30.39</cbc:LineExtensionAmount>
		<cac:Item>
			<cbc:Name>Adjustment - reverse prior Supply charge</cbc:Name>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>S</cbc:ID>
				<cbc:Percent>10</cbc:Percent><!-- 10% GST -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="AUD">0.9803</cbc:PriceAmount>
		</cac:Price>
	</cac:InvoiceLine>
</Invoice>
