<?xml version="1.0" encoding="ISO-8859-1"?>		
<!--
Punch Out: Spring 2020 release compliant
-->				
<Catalogue xmlns="urn:oasis:names:specification:ubl:schema:xsd:Catalogue-2"						
		   xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"				
		   xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">				
	<cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:punch_out:3</cbc:CustomizationID>					
	<cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:punch_out:3</cbc:ProfileID>					
	<cbc:ID>990863</cbc:ID>					
	<cbc:ActionCode>true</cbc:ActionCode>					
	<cbc:IssueDate>2020-08-01</cbc:IssueDate>					
	<cbc:IssueTime>09:00:00</cbc:IssueTime>					
	<cac:ValidityPeriod>					
		<cbc:EndDate>2021-08-31</cbc:EndDate>		
		<cbc:EndTime>18:00:00</cbc:EndTime>		
	</cac:ValidityPeriod>					
	<cac:ReferencedContract>					
		<cbc:ID>REF12345</cbc:ID>				
	</cac:ReferencedContract>					
	<cac:ProviderParty>					
		<cbc:EndpointID schemeID="0088">9429033821733</cbc:EndpointID>				
		<cac:PartyIdentification>				
			<cbc:ID schemeID="0088">9429033821733</cbc:ID>			
		</cac:PartyIdentification>				
		<cac:PartyLegalEntity>				
			<cbc:RegistrationName>Stationery Warehouse Limited</cbc:RegistrationName>			
		</cac:PartyLegalEntity>				
	</cac:ProviderParty>					
	<cac:ReceiverParty>					
		<cbc:EndpointID schemeID="0088">9429033591476</cbc:EndpointID>				
		<cac:PartyIdentification>				
			<cbc:ID schemeID="0088">9429033591476</cbc:ID>			
		</cac:PartyIdentification>				
		<cac:PartyLegalEntity>				
			<cbc:RegistrationName>Eketahuna Enterprises Inc.</cbc:RegistrationName>			
		</cac:PartyLegalEntity>				
		<cac:Contact>				
			<cbc:ID>J C Admundsen</cbc:ID>			
		</cac:Contact>				
	</cac:ReceiverParty>					
	<cac:CatalogueLine>					
		<cbc:ID>1</cbc:ID>				
		<cac:LineValidityPeriod>				
			<cbc:StartDate>2020-01-01</cbc:StartDate>			
			<cbc:EndDate>2020-12-31</cbc:EndDate>			
			<cbc:EndTime>12:00:00</cbc:EndTime>			
		</cac:LineValidityPeriod>				
		<cac:RequiredItemLocationQuantity>				
			<cbc:LeadTimeMeasure unitCode="DAY">2</cbc:LeadTimeMeasure>			
			<cac:ApplicableTerritoryAddress>			
				<cbc:StreetName>Gate A</cbc:StreetName>		
				<cbc:AdditionalStreetName>916 Herbert Street</cbc:AdditionalStreetName>		
				<cbc:CityName>Eketahuna</cbc:CityName>		
				<cbc:PostalZone>4900</cbc:PostalZone>		
				<cbc:CountrySubentity>North Island</cbc:CountrySubentity>		
				<cac:AddressLine>		
					<cbc:Line>Warehouse B/Lot 4</cbc:Line>	
				</cac:AddressLine>		
				<cac:Country>		
					<cbc:IdentificationCode>NZ</cbc:IdentificationCode>	
				</cac:Country>		
			</cac:ApplicableTerritoryAddress>			
			<cac:Price>			
				<cbc:PriceAmount currencyID="NZD">10.00</cbc:PriceAmount>		
				<cbc:BaseQuantity unitCode="C62">1</cbc:BaseQuantity>		
				<cbc:PriceType>AAA</cbc:PriceType>		
			</cac:Price>			
			<cac:DeliveryUnit>			
				<cbc:BatchQuantity unitCode="C62">1</cbc:BatchQuantity>		
			</cac:DeliveryUnit>			
		</cac:RequiredItemLocationQuantity>				
		<cac:Item>				
			<cbc:Description>Photo copy paper 80g A4, package of 500 sheets.</cbc:Description>			
			<cbc:Name>Copy paper</cbc:Name>			
			<cac:SellersItemIdentification>			
				<cbc:ID>FD3004-12</cbc:ID>		
			</cac:SellersItemIdentification>			
			<cac:ManufacturersItemIdentification>			
				<cbc:ID>FD3004</cbc:ID>		
			</cac:ManufacturersItemIdentification>			
			<cac:StandardItemIdentification>			
				<cbc:ID schemeID="0160">1234567890114</cbc:ID>		
			</cac:StandardItemIdentification>			
			<cac:ItemSpecificationDocumentReference>			
				<cbc:ID>12345</cbc:ID>
				<cbc:DocumentTypeCode>TRADE_ITEM_DESCRIPTION</cbc:DocumentTypeCode>
				<cbc:DocumentDescription>Photocopy Paper A4</cbc:DocumentDescription>				
				<cac:Attachment>		
					<cbc:EmbeddedDocumentBinaryObject mimeCode="image/png" filename="image1.png">UjBsR09EbGhjZ0dTQUxNQUFBUUNBRU1tQ1p0dU1GUXhEUzhi</cbc:EmbeddedDocumentBinaryObject>	
				</cac:Attachment>		
			</cac:ItemSpecificationDocumentReference>			
			<cac:OriginCountry>			
				<cbc:IdentificationCode>NZ</cbc:IdentificationCode>		
			</cac:OriginCountry>			
			<cac:CommodityClassification>			
				<cbc:ItemClassificationCode listID="MP" listVersionID="19.0501">20101601</cbc:ItemClassificationCode>		
			</cac:CommodityClassification>			
			<cac:TransactionConditions>			
				<cbc:ActionCode>CT</cbc:ActionCode>		
			</cac:TransactionConditions>			
			<cac:ClassifiedTaxCategory>			
				<cbc:ID>S</cbc:ID>
				<cbc:Percent>18</cbc:Percent>		
				<cac:TaxScheme>		
					<cbc:ID>GST</cbc:ID>	
				</cac:TaxScheme>		
			</cac:ClassifiedTaxCategory>			
			<cac:AdditionalItemProperty>			
				<cbc:Name>Grams</cbc:Name>		
				<cbc:NameCode listID="NN">UOM</cbc:NameCode>		
				<cbc:Value>18</cbc:Value>		
				<cbc:ValueQuantity unitCode="GRM">18</cbc:ValueQuantity>		
			</cac:AdditionalItemProperty>			
			<cac:ManufacturerParty>			
				<cac:PartyName>		
					<cbc:Name>Mataura</cbc:Name>	
				</cac:PartyName>		
			</cac:ManufacturerParty>			
			<cac:Certificate>			
				<cbc:ID>123450</cbc:ID>		
				<cbc:CertificateTypeCode>NA</cbc:CertificateTypeCode>		
				<cbc:CertificateType>Environmental</cbc:CertificateType>		
				<cbc:Remarks>Eco Friendly Certified</cbc:Remarks>		
				<cac:IssuerParty>		
					<cac:PartyName>	
						<cbc:Name>Enviro Safe Org</cbc:Name>
					</cac:PartyName>	
				</cac:IssuerParty>		
			</cac:Certificate>			
		</cac:Item>				
	</cac:CatalogueLine>					
	<cac:CatalogueLine>					
		<cbc:ID>2</cbc:ID>				
		<cac:RequiredItemLocationQuantity>				
			<cac:Price>			
				<cbc:PriceAmount currencyID="NZD">90.00</cbc:PriceAmount>		
				<cbc:BaseQuantity unitCode="C62">1</cbc:BaseQuantity>		
			</cac:Price>			
			<cac:DeliveryUnit>			
				<cbc:BatchQuantity unitCode="C62">1</cbc:BatchQuantity>		
			</cac:DeliveryUnit>			
		</cac:RequiredItemLocationQuantity>				
		<cac:Item>				
			<cbc:Description>Photo copy paper 80g A4, carton of 10 units with 500 sheets each</cbc:Description>			
			<cbc:Name>Copy paper</cbc:Name>			
			<cac:SellersItemIdentification>			
				<cbc:ID>FD012-A</cbc:ID>		
			</cac:SellersItemIdentification>			
			<cac:ManufacturersItemIdentification>			
				<cbc:ID>FD012</cbc:ID>		
			</cac:ManufacturersItemIdentification>			
			<cac:StandardItemIdentification>			
				<cbc:ID schemeID="0160">1234567890124</cbc:ID>		
			</cac:StandardItemIdentification>			
			<cac:CommodityClassification>			
				<cbc:ItemClassificationCode listID="MP" listVersionID="19.0501">20101601</cbc:ItemClassificationCode>		
			</cac:CommodityClassification>			
			<cac:ClassifiedTaxCategory>			
				<cbc:ID>S</cbc:ID>		
				<cbc:Percent>18</cbc:Percent>		
				<cac:TaxScheme>		
					<cbc:ID>GST</cbc:ID>	
				</cac:TaxScheme>		
			</cac:ClassifiedTaxCategory>			
			<cac:AdditionalItemProperty>			
				<cbc:Name>Paper weight in grams</cbc:Name>		
				<cbc:Value>18</cbc:Value>		
			</cac:AdditionalItemProperty>			
		</cac:Item>				
	</cac:CatalogueLine>					
</Catalogue>