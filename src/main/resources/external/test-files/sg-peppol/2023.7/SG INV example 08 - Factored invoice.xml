<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:ccts="urn:un:unece:uncefact:documentation:2" xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" xmlns:qdt="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" xmlns:udt="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

<!-- 
Factored invoice. Include iformation about the Payee who shall receive the payment, i.e. the Factor. The factor shall also be the owner of the account to which the payment shall be made.
-->

	<cbc:UBLVersionID>2.1</cbc:UBLVersionID>
	<cbc:CustomizationID>urn:cen.eu:en16931:2017#conformant#urn:fdc:peppol.eu:2017:poacc:billing:international:sg:3.0</cbc:CustomizationID> <!-- BT-24 -->
	<cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID> <!-- BT-23 -->
	<cbc:ID>F012345</cbc:ID> <!-- BT-1 -->
	<cbc:IssueDate>2018-12-01</cbc:IssueDate> <!-- BT-2 -->
	<cbc:DueDate>2019-01-01</cbc:DueDate> <!-- BT-9 -->
	<cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode> <!-- BT-3 -->
	<cbc:DocumentCurrencyCode>SGD</cbc:DocumentCurrencyCode> <!-- BT-5 -->
	<cbc:BuyerReference>123</cbc:BuyerReference> <!-- BT-10 -->
	<cac:AccountingSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">*************</cbc:EndpointID> <!-- BT-34, BT-34-1 -->
			<cac:PartyIdentification>
				<cbc:ID schemeID="0035">*************</cbc:ID> <!-- BT-29, BT-29-1 -->
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>Mainstreet 112</cbc:StreetName> <!-- BT-35 -->
				<cbc:AdditionalStreetName>Building 3</cbc:AdditionalStreetName> <!-- BT-36 -->
				<cbc:CityName>Singapore</cbc:CityName> <!-- BT-37 -->
				<cbc:PostalZone>1000</cbc:PostalZone> <!-- BT-38 -->
				<cbc:CountrySubentity>Singapore</cbc:CountrySubentity> <!-- BT-39 -->
				<cac:Country>
					<cbc:IdentificationCode>SG</cbc:IdentificationCode> <!-- BT-40 -->
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme>
				<cbc:CompanyID>M2-1234567-K</cbc:CompanyID> <!-- BT-31 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-31, qualifier -->
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Gallery Photo Supplier</cbc:RegistrationName> <!-- BT-27 -->
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingSupplierParty>
	<cac:AccountingCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">*************</cbc:EndpointID> <!-- BT-49, BT-49-1 -->
			<cac:PartyIdentification>
				<cbc:ID schemeID="0035">345KS5324</cbc:ID> <!-- BT-46, BT-46-1 -->
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>Central road 56</cbc:StreetName> <!-- BT-50 -->
				<cbc:AdditionalStreetName>Second floor</cbc:AdditionalStreetName> <!-- BT-51 -->
				<cbc:CityName>Singapore</cbc:CityName> <!-- BT-52 -->
				<cbc:PostalZone>101</cbc:PostalZone> <!-- BT-53 -->
				<cbc:CountrySubentity>Singapore</cbc:CountrySubentity> <!-- BT-54 -->
				<cac:Country>
					<cbc:IdentificationCode>SG</cbc:IdentificationCode> <!-- BT-55 -->
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>IMDA</cbc:RegistrationName> <!-- BT-45 -->
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingCustomerParty>
	<cac:PayeeParty>	
		<cac:PartyIdentification>
			<cbc:ID schemeID="0035">Payee123</cbc:ID> <!-- BT-60, BT-60-1 -->
		</cac:PartyIdentification>
		<cac:PartyName>
			<cbc:Name>Faktor Inc</cbc:Name> <!-- BT-59 -->
		</cac:PartyName>
		<cac:PartyLegalEntity>
			<cbc:CompanyID>**********</cbc:CompanyID> <!-- BT-61, BT-61-1 -->
		</cac:PartyLegalEntity>
	</cac:PayeeParty>
	<cac:PaymentMeans>
		<cbc:PaymentMeansCode name="Bank transfer">30</cbc:PaymentMeansCode> <!-- BT-82, BT-81 -->
		<cbc:PaymentID>gr12345</cbc:PaymentID> <!-- BT-83 -->
		<cac:PayeeFinancialAccount>
			<cbc:ID>************</cbc:ID> <!-- BT-84 -->
			<cbc:Name>Payee current account</cbc:Name> <!-- BT-85 -->
			<cac:FinancialInstitutionBranch>
				<cbc:ID>ICDLOG</cbc:ID> <!-- BT-86 -->
			</cac:FinancialInstitutionBranch>
		</cac:PayeeFinancialAccount>
	</cac:PaymentMeans>
	<cac:PaymentTerms>
		<cbc:Note>Late fees of 1% charged from due date</cbc:Note> <!-- BT-20 -->
	</cac:PaymentTerms>
    <cac:AllowanceCharge>
        <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
        <cbc:AllowanceChargeReason>Cleaning</cbc:AllowanceChargeReason>
        <cbc:Amount currencyID="SGD">100</cbc:Amount>
        <cac:TaxCategory>
            <cbc:ID>SR</cbc:ID>
            <cbc:Percent>7</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID>GST</cbc:ID>
            </cac:TaxScheme>
        </cac:TaxCategory>
    </cac:AllowanceCharge>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="SGD">66.85</cbc:TaxAmount> <!-- BT-110 -->
		<cac:TaxSubtotal>
			<cbc:TaxableAmount currencyID="SGD">955.00</cbc:TaxableAmount> <!-- BT-116 -->
			<cbc:TaxAmount currencyID="SGD">66.85</cbc:TaxAmount> <!-- BT-117 -->
			<cac:TaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-118 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-119 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-118, qualifier -->
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
	</cac:TaxTotal>
	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="SGD">855.00</cbc:LineExtensionAmount> <!-- BT-106 -->
		<cbc:TaxExclusiveAmount currencyID="SGD">955.00</cbc:TaxExclusiveAmount> <!-- BT-109 -->
		<cbc:TaxInclusiveAmount currencyID="SGD">1021.85</cbc:TaxInclusiveAmount> <!-- BT-112 -->
		<cbc:ChargeTotalAmount currencyID="SGD">100.00</cbc:ChargeTotalAmount> <!-- BT-108 -->
		<cbc:PayableAmount currencyID="SGD">1021.85</cbc:PayableAmount> <!-- BT-115 -->
	</cac:LegalMonetaryTotal>
	<cac:InvoiceLine>
		<cbc:ID>1</cbc:ID> <!-- BT-126 -->
		<cbc:Note>The equipment has 3 year warranty.</cbc:Note> <!-- BT-127 -->
		<cbc:InvoicedQuantity unitCode="H87">10</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="SGD">855.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-138, BT-143 -->
			<cbc:Amount currencyID="SGD">45.00</cbc:Amount> <!-- BT-136, BT-141 -->
			<cbc:BaseAmount currencyID="SGD">900.00</cbc:BaseAmount> <!-- BT-137, BT-142 -->
		</cac:AllowanceCharge>
		<cac:Item>
			<cbc:Name>Yashica MG2</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item1</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">1234567890121</cbc:ID> <!-- BT-157, BT-157-1 -->
			</cac:StandardItemIdentification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
			<cac:AdditionalItemProperty>
				<cbc:Name>Colour</cbc:Name> <!-- BT-160 -->
				<cbc:Value>Black</cbc:Value> <!-- BT-161 -->
			</cac:AdditionalItemProperty>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="SGD">90.00</cbc:PriceAmount> <!-- BT-146 -->
			<cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity> <!-- BT-149, BT-150 -->
		</cac:Price>
	</cac:InvoiceLine>
</Invoice>