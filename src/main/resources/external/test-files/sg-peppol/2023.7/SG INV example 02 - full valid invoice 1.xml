<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:ccts="urn:un:unece:uncefact:documentation:2" xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" xmlns:qdt="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" xmlns:udt="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

<!-- 
Singapore invoice
-->

	<cbc:UBLVersionID>2.1</cbc:UBLVersionID>
	<cbc:CustomizationID>urn:cen.eu:en16931:2017#conformant#urn:fdc:peppol.eu:2017:poacc:billing:international:sg:3.0</cbc:CustomizationID> <!-- BT-24 -->
	<cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID> <!-- BT-23 -->
	<cbc:ID>F012345</cbc:ID> <!-- BT-1 -->
	<cbc:IssueDate>2018-12-01</cbc:IssueDate> <!-- BT-2 -->
	<cbc:DueDate>2019-01-01</cbc:DueDate> <!-- BT-9 -->
	<cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode> <!-- BT-3 -->
	<cbc:Note>Ordered at trade show</cbc:Note> <!-- BT-22 -->
	<cbc:DocumentCurrencyCode>SGD</cbc:DocumentCurrencyCode> <!-- BT-5 -->
	<cbc:AccountingCost>102035</cbc:AccountingCost> <!-- BT-19 -->
	<cbc:BuyerReference>123</cbc:BuyerReference> <!-- BT-10 -->
	<cac:InvoicePeriod>
		<cbc:StartDate>2018-11-01</cbc:StartDate> <!-- BT-73 -->
		<cbc:EndDate>2018-11-30</cbc:EndDate> <!-- BT-74 -->
	</cac:InvoicePeriod>
	<cac:OrderReference>
		<cbc:ID>123</cbc:ID> <!-- BT-13 -->
		<cbc:SalesOrderID>123</cbc:SalesOrderID>  <!-- BT-14 -->
	</cac:OrderReference>
	<cac:BillingReference>
		<cac:InvoiceDocumentReference>
			<cbc:ID>invnr002</cbc:ID>  <!-- BT-25 -->
			<cbc:IssueDate>2018-06-01</cbc:IssueDate>  <!-- BT-26 -->
		</cac:InvoiceDocumentReference>
	</cac:BillingReference>
	<cac:DespatchDocumentReference>
		<cbc:ID>987</cbc:ID>  <!-- BT-16 -->
	</cac:DespatchDocumentReference>
	<cac:ReceiptDocumentReference>
		<cbc:ID>654</cbc:ID>  <!-- BT-15 -->
	</cac:ReceiptDocumentReference>
	<cac:OriginatorDocumentReference>
		<cbc:ID>753</cbc:ID>  <!-- BT-17 -->
	</cac:OriginatorDocumentReference>
	<cac:ContractDocumentReference>
		<cbc:ID>Contract321</cbc:ID> <!-- BT-12 -->
	</cac:ContractDocumentReference>
	<cac:AdditionalDocumentReference>
		<cbc:ID>doc1</cbc:ID>  <!-- BT-122 -->
		<cbc:DocumentDescription>Usage breakdown</cbc:DocumentDescription>  <!-- BT-123 -->
		<cac:Attachment>
			<cac:ExternalReference>
				<cbc:URI>http://www.salescompany.be/breakdown001.html</cbc:URI>  <!-- BT-124 -->
			</cac:ExternalReference>
		</cac:Attachment>
	</cac:AdditionalDocumentReference>
	<cac:AdditionalDocumentReference>
		<cbc:ID>doc2</cbc:ID>  <!-- BT-122 -->
		<cbc:DocumentDescription>Usage summary</cbc:DocumentDescription>  <!-- BT-123 -->
		<cac:Attachment>
			<cbc:EmbeddedDocumentBinaryObject filename="report.csv" mimeCode="text/csv">aHR0cHM6Ly90ZXN0LXZlZmEuZGlmaS5uby9wZXBwb2xiaXMvcG9hY2MvYmlsbGluZy8zLjAvYmlzLw==</cbc:EmbeddedDocumentBinaryObject>   <!-- BT-125 -->
		</cac:Attachment>
	</cac:AdditionalDocumentReference>	
	<cac:AdditionalDocumentReference>
		<cbc:ID schemeID="ABZ">951</cbc:ID>  <!-- BT-18 -->
		<cbc:DocumentTypeCode>130</cbc:DocumentTypeCode>  <!-- BT-18 qualifier -->
	</cac:AdditionalDocumentReference>
	<cac:ProjectReference>
		<cbc:ID>321</cbc:ID> <!-- BT-11 -->
	</cac:ProjectReference>
	<cac:AccountingSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">*************</cbc:EndpointID> <!-- BT-34, BT-34-1 -->
			<cac:PartyIdentification>
				<cbc:ID schemeID="0035">*************</cbc:ID> <!-- BT-29, BT-29-1 -->
			</cac:PartyIdentification>
					<cac:PartyName>
				<cbc:Name>Sales trade name</cbc:Name> <!-- BT-28 -->
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>Mainstreet 112</cbc:StreetName> <!-- BT-35 -->
				<cbc:AdditionalStreetName>Building 3</cbc:AdditionalStreetName> <!-- BT-36 -->
				<cbc:CityName>Singapore</cbc:CityName> <!-- BT-37 -->
				<cbc:PostalZone>1000</cbc:PostalZone> <!-- BT-38 -->
				<cbc:CountrySubentity>Singapore</cbc:CountrySubentity> <!-- BT-39 -->
				<cac:AddressLine>
					<cbc:Line>Sales department</cbc:Line> <!-- BT-162 -->
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>SG</cbc:IdentificationCode> <!-- BT-40 -->
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme>
				<cbc:CompanyID>M2-1234567-K</cbc:CompanyID> <!-- BT-31 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-31, qualifier -->
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Gallery Photo Supplier</cbc:RegistrationName> <!-- BT-27 -->
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:Name>John Doe</cbc:Name>  <!-- BT-41 -->
				<cbc:Telephone>4621230</cbc:Telephone> <!-- BT-42 -->
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail> <!-- BT-43 -->
			</cac:Contact>
		</cac:Party>
	</cac:AccountingSupplierParty>
	<cac:AccountingCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">*************</cbc:EndpointID> <!-- BT-49, BT-49-1 -->
			<cac:PartyIdentification>
				<cbc:ID schemeID="0035">345KS5324</cbc:ID> <!-- BT-46, BT-46-1 -->
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Buyer trade name</cbc:Name> <!-- BT-44 -->
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>Central road 56</cbc:StreetName> <!-- BT-50 -->
				<cbc:AdditionalStreetName>Second floor</cbc:AdditionalStreetName> <!-- BT-51 -->
				<cbc:CityName>Singapore</cbc:CityName> <!-- BT-52 -->
				<cbc:PostalZone>101</cbc:PostalZone> <!-- BT-53 -->
				<cbc:CountrySubentity>Singapore</cbc:CountrySubentity> <!-- BT-54 -->
				<cac:AddressLine>
					<cbc:Line>Accounting department</cbc:Line> <!-- BT-163 -->
				</cac:AddressLine>				
				<cac:Country>
					<cbc:IdentificationCode>SG</cbc:IdentificationCode> <!-- BT-55 -->
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>IMDA</cbc:RegistrationName> <!-- BT-45 -->
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:Name>Bill</cbc:Name> <!-- BT-56 -->
				<cbc:Telephone>5121230</cbc:Telephone> <!-- BT-57 -->
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail> <!-- BT-58 -->
			</cac:Contact>
		</cac:Party>
	</cac:AccountingCustomerParty>
	<cac:PayeeParty>	
		<cac:PartyIdentification>
			<cbc:ID schemeID="0035">Payee123</cbc:ID> <!-- BT-60, BT-60-1 -->
		</cac:PartyIdentification>
		<cac:PartyName>
			<cbc:Name>Faktor Inc</cbc:Name> <!-- BT-59 -->
		</cac:PartyName>
		<cac:PartyLegalEntity>
			<cbc:CompanyID>**********</cbc:CompanyID> <!-- BT-61, BT-61-1 -->
		</cac:PartyLegalEntity>
	</cac:PayeeParty>
	<cac:TaxRepresentativeParty>	
		<cac:PartyName>
			<cbc:Name>TaxRepresentative name</cbc:Name> <!-- BT-62 -->
		</cac:PartyName>
		<cac:PostalAddress>
			<cbc:StreetName>Rue Cler 99</cbc:StreetName> <!-- BT-64 -->
			<cbc:AdditionalStreetName>Ground floor</cbc:AdditionalStreetName> <!-- BT-65 -->
			<cbc:CityName>Paris</cbc:CityName> <!-- BT-66 -->
			<cbc:PostalZone>220</cbc:PostalZone> <!-- BT-67 -->
			<cbc:CountrySubentity>Île-de-France</cbc:CountrySubentity> <!-- BT-68 -->
			<cac:AddressLine>
				<cbc:Line>Tax service department</cbc:Line> <!-- BT-164 -->
			</cac:AddressLine>				
			<cac:Country>
				<cbc:IdentificationCode>FR</cbc:IdentificationCode> <!-- BT-69 -->
			</cac:Country>
		</cac:PostalAddress>
		<cac:PartyTaxScheme>
			<cbc:CompanyID>FR98746</cbc:CompanyID> <!-- BT-63 -->
			<cac:TaxScheme>
				<cbc:ID>GST</cbc:ID> <!-- BT-63, qualifier -->
			</cac:TaxScheme>
		</cac:PartyTaxScheme>
	</cac:TaxRepresentativeParty>
	<cac:Delivery>
		<cbc:ActualDeliveryDate>2010-08-31</cbc:ActualDeliveryDate> <!-- BT-72 -->		
		<cac:DeliveryLocation>
			<cbc:ID schemeID="0035">6754238987648</cbc:ID> <!-- BT-71, BT-71-1 -->
			<cac:Address>
				<cbc:StreetName>Coolsingel Rotterdam 12</cbc:StreetName> <!-- BT-75 -->
				<cbc:AdditionalStreetName>By the big house</cbc:AdditionalStreetName> <!-- BT-76 -->
				<cbc:CityName>Rotterdam</cbc:CityName> <!-- BT-77 -->
				<cbc:PostalZone>700</cbc:PostalZone> <!-- BT-78 -->
				<cbc:CountrySubentity>South Holland</cbc:CountrySubentity> <!-- BT-79 -->
				<cac:AddressLine>
					<cbc:Line>Delivery department</cbc:Line> <!-- BT-165 -->
				</cac:AddressLine>				
				<cac:Country>
					<cbc:IdentificationCode>SG</cbc:IdentificationCode> <!-- BT-80 -->
				</cac:Country>
			</cac:Address>
		</cac:DeliveryLocation>
		<cac:DeliveryParty>
			<cac:PartyName>
				<cbc:Name>Delivery services Inc.</cbc:Name> <!-- BT-70 -->
			</cac:PartyName>
		</cac:DeliveryParty>
	</cac:Delivery>
	<cac:PaymentMeans>
		<cbc:PaymentMeansCode name="Bank transfer">30</cbc:PaymentMeansCode> <!-- BT-82, BT-81 -->
		<cbc:PaymentID>gr12345</cbc:PaymentID> <!-- BT-83 -->
		<cac:PayeeFinancialAccount>
			<cbc:ID>************</cbc:ID> <!-- BT-84 -->
			<cbc:Name>Payee current account</cbc:Name> <!-- BT-85 -->
			<cac:FinancialInstitutionBranch>
				<cbc:ID>ICDLOG</cbc:ID> <!-- BT-86 -->
			</cac:FinancialInstitutionBranch>
		</cac:PayeeFinancialAccount>
	</cac:PaymentMeans>
	<cac:PaymentTerms>
		<cbc:Note>Late fees of 1% charged from due date</cbc:Note> <!-- BT-20 -->
	</cac:PaymentTerms>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="SGD">2374.05</cbc:TaxAmount> <!-- BT-110 -->
		<cac:TaxSubtotal>
			<cbc:TaxableAmount currencyID="SGD">33915.00</cbc:TaxableAmount> <!-- BT-116 -->
			<cbc:TaxAmount currencyID="SGD">2374.05</cbc:TaxAmount> <!-- BT-117 -->
			<cac:TaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-118 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-119 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-118, qualifier -->
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
	</cac:TaxTotal>
	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="SGD">33915.00</cbc:LineExtensionAmount> <!-- BT-106 -->
		<cbc:TaxExclusiveAmount currencyID="SGD">33915.00</cbc:TaxExclusiveAmount> <!-- BT-109 -->
		<cbc:TaxInclusiveAmount currencyID="SGD">36289.05</cbc:TaxInclusiveAmount> <!-- BT-112 -->
		<cbc:AllowanceTotalAmount currencyID="SGD">0.00</cbc:AllowanceTotalAmount> <!-- BT-107 -->
		<cbc:ChargeTotalAmount currencyID="SGD">0.00</cbc:ChargeTotalAmount> <!-- BT-108 -->
		<cbc:PrepaidAmount currencyID="SGD">0.00</cbc:PrepaidAmount> <!-- BT-113 -->
		<cbc:PayableRoundingAmount currencyID="SGD">0.0</cbc:PayableRoundingAmount> <!-- BT-114 -->
		<cbc:PayableAmount currencyID="SGD">36289.05</cbc:PayableAmount> <!-- BT-115 -->
	</cac:LegalMonetaryTotal>
	<cac:InvoiceLine>
		<cbc:ID>1</cbc:ID> <!-- BT-126 -->
		<cbc:Note>The equipment has 3 year warranty.</cbc:Note> <!-- BT-127 -->
		<cbc:InvoicedQuantity unitCode="H87">10</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="SGD">855.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cbc:AccountingCost>Cost id 654</cbc:AccountingCost> <!-- BT-133 -->
		<cac:InvoicePeriod>
			<cbc:StartDate>2018-11-01</cbc:StartDate> <!-- BT-134 -->
			<cbc:EndDate>2018-11-30</cbc:EndDate> <!-- BT-135 -->
		</cac:InvoicePeriod>
		<cac:OrderLineReference>
			<cbc:LineID>1</cbc:LineID> <!-- BT-132 -->
		</cac:OrderLineReference>
		<cac:DocumentReference>
			<cbc:ID schemeID="ABZ">AB-123</cbc:ID> <!-- BT-128, BT-128-1 -->
			<cbc:DocumentTypeCode>130</cbc:DocumentTypeCode> <!-- BT-128, qualifier -->
		</cac:DocumentReference>
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-138, BT-143 -->
			<cbc:Amount currencyID="SGD">45.00</cbc:Amount> <!-- BT-136, BT-141 -->
			<cbc:BaseAmount currencyID="SGD">900.00</cbc:BaseAmount> <!-- BT-137, BT-142 -->
		</cac:AllowanceCharge>
		<cac:Item>
			<cbc:Name>Yashica MG2</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item1</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">1234567890121</cbc:ID> <!-- BT-157, BT-157-1 -->
			</cac:StandardItemIdentification>
			<cac:OriginCountry>
				<cbc:IdentificationCode>CH</cbc:IdentificationCode> <!-- BT-159 -->
			</cac:OriginCountry>
			<cac:CommodityClassification>
				<cbc:ItemClassificationCode listID="MP">43211503</cbc:ItemClassificationCode> <!-- BT-158, BT-158-1 -->
			</cac:CommodityClassification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
			<cac:AdditionalItemProperty>
				<cbc:Name>Colour</cbc:Name> <!-- BT-160 -->
				<cbc:Value>Black</cbc:Value> <!-- BT-161 -->
			</cac:AdditionalItemProperty>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="SGD">90.00</cbc:PriceAmount> <!-- BT-146 -->
			<cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity> <!-- BT-149, BT-150 -->
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<cbc:ID>2</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">20</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="SGD">19000.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-138, BT-143 -->
			<cbc:Amount currencyID="SGD">1000.00</cbc:Amount> <!-- BT-136, BT-141 -->
			<cbc:BaseAmount currencyID="SGD">20000.00</cbc:BaseAmount> <!-- BT-137, BT-142 -->
		</cac:AllowanceCharge>		
		<cac:Item>
			<cbc:Name>Pentax Z-1 Body</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item2</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">1234567890121</cbc:ID> <!-- BT-157, BT-157-1 -->
			</cac:StandardItemIdentification>
			<cac:OriginCountry>
				<cbc:IdentificationCode>CH</cbc:IdentificationCode> <!-- BT-159 -->
			</cac:OriginCountry>
			<cac:CommodityClassification>
				<cbc:ItemClassificationCode listID="MP">43211503</cbc:ItemClassificationCode> <!-- BT-158, BT-158-1 -->
			</cac:CommodityClassification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
			</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="SGD">1000.00</cbc:PriceAmount> <!-- BT-146 -->
			<cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity> <!-- BT-149, BT-150 -->
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<cbc:ID>3</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">30</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="SGD">5700.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-138, BT-143 -->
			<cbc:Amount currencyID="SGD">300.00</cbc:Amount> <!-- BT-136, BT-141 -->
			<cbc:BaseAmount currencyID="SGD">6000.00</cbc:BaseAmount> <!-- BT-137, BT-142 -->
		</cac:AllowanceCharge>		
		<cac:Item>
			<cbc:Name>Camera W35</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item3</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">1234567890121</cbc:ID> <!-- BT-157, BT-157-1 -->
			</cac:StandardItemIdentification>
			<cac:OriginCountry>
				<cbc:IdentificationCode>CH</cbc:IdentificationCode> <!-- BT-159 -->
			</cac:OriginCountry>
			<cac:CommodityClassification>
				<cbc:ItemClassificationCode listID="MP">43211503</cbc:ItemClassificationCode> <!-- BT-158, BT-158-1 -->
			</cac:CommodityClassification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="SGD">200.00</cbc:PriceAmount> <!-- BT-146 -->
			<cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity> <!-- BT-149, BT-150 -->
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<cbc:ID>4</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">40</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="SGD">8360.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-138, BT-143 -->
			<cbc:Amount currencyID="SGD">440.00</cbc:Amount> <!-- BT-136, BT-141 -->
			<cbc:BaseAmount currencyID="SGD">8800.00</cbc:BaseAmount> <!-- BT-137, BT-142 -->
		</cac:AllowanceCharge>		
		<cac:Item>
			<cbc:Name>Camera Prima 5</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item4</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">1234567890121</cbc:ID> <!-- BT-157, BT-157-1 -->
			</cac:StandardItemIdentification>
			<cac:OriginCountry>
				<cbc:IdentificationCode>CH</cbc:IdentificationCode> <!-- BT-159 -->
			</cac:OriginCountry>
			<cac:CommodityClassification>
				<cbc:ItemClassificationCode listID="MP">43211503</cbc:ItemClassificationCode> <!-- BT-158, BT-158-1 -->
			</cac:CommodityClassification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="SGD">220.00</cbc:PriceAmount> <!-- BT-146 -->
			<cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity> <!-- BT-149, BT-150 -->
		</cac:Price>
	</cac:InvoiceLine>		
</Invoice>