<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:ccts="urn:un:unece:uncefact:documentation:2" xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" xmlns:qdt="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" xmlns:udt="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

<!-- 
A valid Singapore invoice demonstrating use of Allowances and Charges, both on document and line level.

On document level:
- Allowance calculated as a percentage of a base amount.
- Allowance given as fixed amount.
- Charge calculated as percentage of a base amount.
- Charge given as fixed amount.

On line level:
- Line 1 shows an allowance and a charge that are calculated with percentage of a base amount.
- Line 2 shows an allowance and a charge that are given as fixed amounts.
- Line 3 has no allowances or charges

-->

	<cbc:UBLVersionID>2.1</cbc:UBLVersionID>
	<cbc:CustomizationID>urn:cen.eu:en16931:2017#conformant#urn:fdc:peppol.eu:2017:poacc:billing:international:sg:3.0</cbc:CustomizationID> <!-- BT-24 -->
	<cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID> <!-- BT-23 -->
	<cbc:ID>F012345</cbc:ID> <!-- BT-1 -->
	<cbc:IssueDate>2018-12-01</cbc:IssueDate> <!-- BT-2 -->
	<cbc:DueDate>2019-01-01</cbc:DueDate> <!-- BT-9 -->
	<cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode> <!-- BT-3 -->
	<cbc:DocumentCurrencyCode>SGD</cbc:DocumentCurrencyCode> <!-- BT-5 -->
	<cbc:BuyerReference>123</cbc:BuyerReference> <!-- BT-10 -->
	<cac:AccountingSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">*************</cbc:EndpointID> <!-- BT-34, BT-34-1 -->
			<cac:PartyIdentification>
				<cbc:ID> schemeID="0035">*************</cbc:ID> <!-- BT-29, BT-29-1 -->
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>Mainstreet 112</cbc:StreetName> <!-- BT-35 -->
				<cbc:AdditionalStreetName>Building 3</cbc:AdditionalStreetName> <!-- BT-36 -->
				<cbc:CityName>Singapore</cbc:CityName> <!-- BT-37 -->
				<cbc:PostalZone>1000</cbc:PostalZone> <!-- BT-38 -->
				<cbc:CountrySubentity>Singapore</cbc:CountrySubentity> <!-- BT-39 -->
				<cac:Country>
					<cbc:IdentificationCode>SG</cbc:IdentificationCode> <!-- BT-40 -->
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme>
				<cbc:CompanyID>M2-1234567-K</cbc:CompanyID> <!-- BT-31 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-31, qualifier -->
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Gallery Photo Supplier</cbc:RegistrationName> <!-- BT-27 -->
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingSupplierParty>
	<cac:AccountingCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">*************</cbc:EndpointID> <!-- BT-49, BT-49-1 -->
			<cac:PartyIdentification>
				<cbc:ID schemeID="0035">345KS5324</cbc:ID> <!-- BT-46, BT-46-1 -->
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>Central road 56</cbc:StreetName> <!-- BT-50 -->
				<cbc:AdditionalStreetName>Second floor</cbc:AdditionalStreetName> <!-- BT-51 -->
				<cbc:CityName>Singapore</cbc:CityName> <!-- BT-52 -->
				<cbc:PostalZone>101</cbc:PostalZone> <!-- BT-53 -->
				<cbc:CountrySubentity>Singapore</cbc:CountrySubentity> <!-- BT-54 -->
				<cac:Country>
					<cbc:IdentificationCode>SG</cbc:IdentificationCode> <!-- BT-55 -->
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>IMDA</cbc:RegistrationName> <!-- BT-45 -->
			</cac:PartyLegalEntity>
		</cac:Party>
	</cac:AccountingCustomerParty>
	<cac:PaymentMeans>
		<cbc:PaymentMeansCode name="Bank transfer">30</cbc:PaymentMeansCode> <!-- BT-82, BT-81 -->
		<cbc:PaymentID>gr12345</cbc:PaymentID> <!-- BT-83 -->
		<cac:PayeeFinancialAccount>
			<cbc:ID>************</cbc:ID> <!-- BT-84 -->
			<cbc:Name>Payee current account</cbc:Name> <!-- BT-85 -->
			<cac:FinancialInstitutionBranch>
				<cbc:ID>ICDLOG</cbc:ID> <!-- BT-86 -->
			</cac:FinancialInstitutionBranch>
		</cac:PayeeFinancialAccount>
	</cac:PaymentMeans>
	<cac:PaymentTerms>
		<cbc:Note>Late fees of 1% charged from due date</cbc:Note> <!-- BT-20 -->
	</cac:PaymentTerms>
	<cac:AllowanceCharge>
		<cbc:ChargeIndicator>true</cbc:ChargeIndicator> <!-- UBL qualifier -->
		<cbc:AllowanceChargeReasonCode>DL</cbc:AllowanceChargeReasonCode> <!-- BT-98, BT-105 -->
		<cbc:AllowanceChargeReason>Transport cost</cbc:AllowanceChargeReason> <!-- BT-97, BT-104 -->
		<cbc:Amount currencyID="SGD">2300.00</cbc:Amount> <!-- BT-92, BT-99 -->
		<cac:TaxCategory>
			<cbc:ID>SR</cbc:ID> <!-- BT-95, BT-102 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-96, BT-103  -->
			<cac:TaxScheme>
				<cbc:ID>GST</cbc:ID>
			</cac:TaxScheme>
		</cac:TaxCategory>
	</cac:AllowanceCharge>
	<cac:AllowanceCharge>
		<cbc:ChargeIndicator>true</cbc:ChargeIndicator> <!-- UBL qualifier -->
		<cbc:AllowanceChargeReasonCode>AAI</cbc:AllowanceChargeReasonCode> <!-- BT-98, BT-105 -->
		<cbc:AllowanceChargeReason>Inspection fee</cbc:AllowanceChargeReason> <!-- BT-97, BT-104 -->
		<cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric> <!-- BT-94, BT-101 -->
		<cbc:Amount currencyID="SGD">2600.00</cbc:Amount> <!-- BT-92, BT-99 -->
		<cbc:BaseAmount currencyID="SGD">26000</cbc:BaseAmount> <!-- BT-93, BT-100 -->
		<cac:TaxCategory>
			<cbc:ID>ZR</cbc:ID> <!-- BT-95, BT-102 -->
				<cbc:Percent>0</cbc:Percent> <!-- BT-96, BT-103  -->
			<cac:TaxScheme>
				<cbc:ID>GST</cbc:ID>
			</cac:TaxScheme>
		</cac:TaxCategory>
	</cac:AllowanceCharge>
	<cac:AllowanceCharge>
		<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- UBL qualifier -->
		<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-98, BT-105 -->
		<cbc:AllowanceChargeReason>Value discount</cbc:AllowanceChargeReason> <!-- BT-97, BT-104 -->
		<cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric> <!-- BT-94, BT-101 -->
		<cbc:Amount currencyID="SGD">250.00</cbc:Amount> <!-- BT-92, BT-99 -->
		<cbc:BaseAmount currencyID="SGD">10000</cbc:BaseAmount> <!-- BT-93, BT-100 -->
		<cac:TaxCategory>
			<cbc:ID>ZR</cbc:ID> <!-- BT-95, BT-102 -->
				<cbc:Percent>0</cbc:Percent> <!-- BT-96, BT-103  -->
			<cac:TaxScheme>
				<cbc:ID>GST</cbc:ID>
			</cac:TaxScheme>
		</cac:TaxCategory>
	</cac:AllowanceCharge>
	<cac:AllowanceCharge>
		<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- UBL qualifier -->
		<cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode> <!-- BT-98, BT-105 -->
		<cbc:AllowanceChargeReason>First order discount</cbc:AllowanceChargeReason> <!-- BT-97, BT-104 -->
		<cbc:Amount currencyID="SGD">70.00</cbc:Amount> <!-- BT-92, BT-99 -->
		<cac:TaxCategory>
			<cbc:ID>SR</cbc:ID> <!-- BT-95, BT-102 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-96, BT-103  -->
			<cac:TaxScheme>
				<cbc:ID>GST</cbc:ID>
			</cac:TaxScheme>
		</cac:TaxCategory>
	</cac:AllowanceCharge>	
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="SGD">1575.74</cbc:TaxAmount> <!-- BT-110 -->
		<cac:TaxSubtotal>
			<cbc:TaxableAmount currencyID="SGD">22510.50</cbc:TaxableAmount> <!-- BT-116 -->
			<cbc:TaxAmount currencyID="SGD">1575.74</cbc:TaxAmount> <!-- BT-117 -->
			<cac:TaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-118 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-119 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-118, qualifier -->
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
		<cac:TaxSubtotal>
			<cbc:TaxableAmount currencyID="SGD">8350.00</cbc:TaxableAmount> <!-- BT-116 -->
			<cbc:TaxAmount currencyID="SGD">0</cbc:TaxAmount> <!-- BT-117 -->
			<cac:TaxCategory>
				<cbc:ID>ZR</cbc:ID> <!-- BT-118 -->
				<cbc:Percent>0</cbc:Percent> <!-- BT-119 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID> <!-- BT-118, qualifier -->
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>
	</cac:TaxTotal>
	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="SGD">26280.50</cbc:LineExtensionAmount> <!-- BT-106 -->
		<cbc:TaxExclusiveAmount currencyID="SGD">30860.50</cbc:TaxExclusiveAmount> <!-- BT-109 -->
		<cbc:TaxInclusiveAmount currencyID="SGD">32436.24</cbc:TaxInclusiveAmount> <!-- BT-112 -->
		<cbc:AllowanceTotalAmount currencyID="SGD">320.00</cbc:AllowanceTotalAmount> <!-- BT-107 -->
		<cbc:ChargeTotalAmount currencyID="SGD">4900</cbc:ChargeTotalAmount> <!-- BT-108 -->
		<cbc:PayableAmount currencyID="SGD">32436.24</cbc:PayableAmount> <!-- BT-115 -->
	</cac:LegalMonetaryTotal>
	<cac:InvoiceLine>
		<cbc:ID>1</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">10</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="SGD">940.50</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>5</cbc:MultiplierFactorNumeric> <!-- BT-94, BT-101 -->
			<cbc:Amount currencyID="SGD">49.50</cbc:Amount> <!-- BT-92, BT-99 -->
			<cbc:BaseAmount currencyID="SGD">990.00</cbc:BaseAmount> <!-- BT-93, BT-100 -->
		</cac:AllowanceCharge>
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>true</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>AAZ</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line charge</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric> <!-- BT-94, BT-101 -->
			<cbc:Amount currencyID="SGD">90.00</cbc:Amount> <!-- BT-92, BT-99 -->
			<cbc:BaseAmount currencyID="SGD">900.00</cbc:BaseAmount> <!-- BT-93, BT-100 -->
		</cac:AllowanceCharge>
		<cac:Item>
			<cbc:Name>Yashica MG2</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item1</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="SGD">90.00</cbc:PriceAmount> <!-- BT-146 -->
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<cbc:ID>2</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">20</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="SGD">19340.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>true</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>ABK</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line charge</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:Amount currencyID="SGD">340.00</cbc:Amount> <!-- BT-136, BT-141 -->
		</cac:AllowanceCharge>
		<cac:AllowanceCharge>
			<cbc:ChargeIndicator>false</cbc:ChargeIndicator> <!-- qualifier -->
			<cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode> <!-- BT-140, BT-145 -->
			<cbc:AllowanceChargeReason>Line discount</cbc:AllowanceChargeReason> <!-- BT-139, BT-144 -->
			<cbc:Amount currencyID="SGD">1000.00</cbc:Amount> <!-- BT-136, BT-141 -->
		</cac:AllowanceCharge>			
		<cac:Item>
			<cbc:Name>Pentax Z-1 Body</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item2</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>SR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>7</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
			</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="SGD">1000.00</cbc:PriceAmount> <!-- BT-146 -->
		</cac:Price>
	</cac:InvoiceLine>
	<cac:InvoiceLine>
		<cbc:ID>3</cbc:ID> <!-- BT-126 -->
		<cbc:InvoicedQuantity unitCode="H87">30</cbc:InvoicedQuantity> <!-- BT-130, BT-129 -->
		<cbc:LineExtensionAmount currencyID="SGD">6000.00</cbc:LineExtensionAmount> <!-- BT-131 -->
		<cac:Item>
			<cbc:Name>Camera W35</cbc:Name> <!-- BT-153 -->
			<cac:SellersItemIdentification>
				<cbc:ID>Item3</cbc:ID> <!-- BT-155 -->
			</cac:SellersItemIdentification>
			<cac:ClassifiedTaxCategory>
				<cbc:ID>ZR</cbc:ID> <!-- BT-151 -->
				<cbc:Percent>0</cbc:Percent> <!-- BT-152 -->
				<cac:TaxScheme>
					<cbc:ID>GST</cbc:ID>
				</cac:TaxScheme>
			</cac:ClassifiedTaxCategory>
		</cac:Item>
		<cac:Price>
			<cbc:PriceAmount currencyID="SGD">200.00</cbc:PriceAmount> <!-- BT-146 -->
		</cac:Price>
	</cac:InvoiceLine>
</Invoice>