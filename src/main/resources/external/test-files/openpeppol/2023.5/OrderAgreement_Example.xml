<?xml version="1.0" encoding="UTF-8"?>
<!--
                Content:
                This file contains a Full BIS3 PEPPOL UBL Order Agreement

                Errors:
                None
                
                Warnings:
                None
				
-->
<OrderResponse xmlns="urn:oasis:names:specification:ubl:schema:xsd:OrderResponse-2"
			   xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
			   xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
	<cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:order_agreement:3</cbc:CustomizationID>
	<cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:order_agreement:3</cbc:ProfileID>
	<cbc:ID>0263bf48-9a55-4d15-adf5-2c2921036d1c</cbc:ID>
	<cbc:SalesOrderID>101-111</cbc:SalesOrderID>
	<cbc:IssueDate>2013-07-01</cbc:IssueDate>
	<cbc:IssueTime>06:10:10</cbc:IssueTime>
	<cbc:Note>We have a new phone number 33 44 55</cbc:Note>
	<cbc:DocumentCurrencyCode>EUR</cbc:DocumentCurrencyCode>
	<cbc:CustomerReference>ABC-123</cbc:CustomerReference>
	<cac:OrderReference>
		<cbc:ID>11233</cbc:ID>
	</cac:OrderReference>
	<cac:OriginatorDocumentReference>
		<cbc:ID>123456</cbc:ID>
	</cac:OriginatorDocumentReference>
	<cac:AdditionalDocumentReference>
		<cbc:ID>147852</cbc:ID>
		<cbc:DocumentType>Timesheet</cbc:DocumentType>
		<cac:Attachment>
			<cbc:EmbeddedDocumentBinaryObject mimeCode="image/tiff" filename="hours-spend.csv">UjBsR09EbGhjZ0dTQUxNQUFBUUNBRU1tQ1p0dU1GUXhEUzhi</cbc:EmbeddedDocumentBinaryObject>
			<cac:ExternalReference>
				<cbc:URI>http://www.example.com/index.html</cbc:URI>
			</cac:ExternalReference>
		</cac:Attachment>
	</cac:AdditionalDocumentReference>
	<cac:Contract>
		<cbc:ID>CON-12345</cbc:ID>
	</cac:Contract>
	<cac:SellerSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">7598000000128</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0184">**********</cbc:ID>
			</cac:PartyIdentification>
		
			<cac:PostalAddress>
				<cbc:StreetName>Storgt. 12</cbc:StreetName>
				<cbc:AdditionalStreetName>4. etasje</cbc:AdditionalStreetName>
				<cbc:CityName>Oslo</cbc:CityName>
				<cbc:PostalZone>0585</cbc:PostalZone>
				<cbc:CountrySubentity>Region</cbc:CountrySubentity>
				<cac:AddressLine>
					<cbc:Line>Address Line 3</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>NO</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>The Supplier AB</cbc:RegistrationName>
				<cbc:CompanyID schemeID="0082">*********</cbc:CompanyID>
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:Name>John Doe</cbc:Name>
				<cbc:Telephone>11223344</cbc:Telephone>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
	</cac:SellerSupplierParty>
	<cac:BuyerCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">7590000012347</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0184">DK55412777</cbc:ID>
			</cac:PartyIdentification>
			<cac:PostalAddress>
				<cbc:StreetName>Sinsenveien 40</cbc:StreetName>
				<cbc:AdditionalStreetName>Oppgang B</cbc:AdditionalStreetName>
				<cbc:CityName>Oslo</cbc:CityName>
				<cbc:PostalZone>0501</cbc:PostalZone>
				<cbc:CountrySubentity>Region</cbc:CountrySubentity>
				<cac:AddressLine>
					<cbc:Line>Address Line 3</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>NO</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>City Hospital</cbc:RegistrationName>
				<cbc:CompanyID schemeID="0082">*********</cbc:CompanyID>
			</cac:PartyLegalEntity>
		</cac:Party>
		<cac:DeliveryContact>
			<cbc:Name>Peter Petersen</cbc:Name>
			<cbc:Telephone>********</cbc:Telephone>
			<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
		</cac:DeliveryContact>
	</cac:BuyerCustomerParty>
	<cac:OriginatorCustomerParty>
		<cac:Party>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0184">DK55412777</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Helseavdeling</cbc:Name>
			</cac:PartyName>
		</cac:Party>
	</cac:OriginatorCustomerParty>
	<cac:AccountingCustomerParty>
		<cac:Party>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0184">DK55412777</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Accounting</cbc:Name>
			</cac:PartyName>
		</cac:Party>
	</cac:AccountingCustomerParty>

	<cac:AllowanceCharge>
		<cbc:ChargeIndicator>true</cbc:ChargeIndicator>
		<cbc:AllowanceChargeReasonCode>ABK</cbc:AllowanceChargeReasonCode>
		<cbc:AllowanceChargeReason>Miscellaneous services</cbc:AllowanceChargeReason>
<!--	<cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
-->		<cbc:Amount currencyID="EUR">2.00</cbc:Amount>
<!--	<cbc:BaseAmount currencyID="EUR">32.50</cbc:BaseAmount>
-->		<cac:TaxCategory>
			<cbc:ID>S</cbc:ID>
			<cbc:Percent>25</cbc:Percent>
			<cac:TaxScheme>
				<cbc:ID>VAT</cbc:ID>
			</cac:TaxScheme>
		</cac:TaxCategory>
	</cac:AllowanceCharge>
	<cac:AllowanceCharge>
		<cbc:ChargeIndicator>false</cbc:ChargeIndicator>
		<cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
		<cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
		<cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
		<cbc:Amount currencyID="EUR">3.25</cbc:Amount>
		<cbc:BaseAmount currencyID="EUR">32.50</cbc:BaseAmount>
		<cac:TaxCategory>
			<cbc:ID>S</cbc:ID>
			<cbc:Percent>25</cbc:Percent>
			<cac:TaxScheme>
				<cbc:ID>VAT</cbc:ID>
			</cac:TaxScheme>
		</cac:TaxCategory>
	</cac:AllowanceCharge>
	<cac:TaxTotal>
		<cbc:TaxAmount currencyID="EUR">7.81</cbc:TaxAmount>
		<cac:TaxSubtotal>
			<cbc:TaxableAmount currencyID="EUR">31.25</cbc:TaxableAmount>
			<cbc:TaxAmount currencyID="EUR">7.81</cbc:TaxAmount>
			<cac:TaxCategory>
				<cbc:ID>S</cbc:ID>
				<cbc:Percent>25</cbc:Percent>			
				<cac:TaxScheme>
					<cbc:ID>VAT</cbc:ID>
				</cac:TaxScheme>
			</cac:TaxCategory>
		</cac:TaxSubtotal>


	</cac:TaxTotal>
	<cac:LegalMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="EUR">32.5</cbc:LineExtensionAmount>
		<cbc:TaxExclusiveAmount currencyID="EUR">31.25</cbc:TaxExclusiveAmount>
		<cbc:TaxInclusiveAmount currencyID="EUR">39.06</cbc:TaxInclusiveAmount>
		<cbc:AllowanceTotalAmount currencyID="EUR">3.25</cbc:AllowanceTotalAmount>
		<cbc:ChargeTotalAmount currencyID="EUR">2.00</cbc:ChargeTotalAmount>
		<cbc:PrepaidAmount currencyID="EUR">10.00</cbc:PrepaidAmount>
		<cbc:PayableRoundingAmount currencyID="EUR">0.94</cbc:PayableRoundingAmount>
		<cbc:PayableAmount currencyID="EUR">30.00</cbc:PayableAmount>
	</cac:LegalMonetaryTotal>
	<cac:OrderLine>
		<cac:LineItem>
			<cbc:ID>1</cbc:ID>
			<cbc:Note>Order line note text</cbc:Note>
			<cbc:Quantity unitCode="C62">15</cbc:Quantity>
			<cbc:LineExtensionAmount currencyID="EUR">22.50</cbc:LineExtensionAmount>
			<cac:Delivery>
				<cbc:Quantity unitCode="C62">15.00</cbc:Quantity>
				<cac:PromisedDeliveryPeriod>
					<cbc:StartDate>2018-08-10</cbc:StartDate>
					<cbc:StartTime>12:00:00</cbc:StartTime>
					<cbc:EndDate>2018-08-12</cbc:EndDate>
					<cbc:EndTime>12:00:00</cbc:EndTime>
				</cac:PromisedDeliveryPeriod>
			</cac:Delivery>
			<cac:Price>
				<cbc:PriceAmount currencyID="EUR">1.50</cbc:PriceAmount>
				<cbc:BaseQuantity unitCode="C62">1</cbc:BaseQuantity>
				<cbc:PriceType>AAA</cbc:PriceType>
				<cac:AllowanceCharge>
					<cbc:ChargeIndicator>false</cbc:ChargeIndicator>
					<cbc:Amount currencyID="EUR">0.20</cbc:Amount>
					<cbc:BaseAmount currencyID="EUR">1.70</cbc:BaseAmount>
				</cac:AllowanceCharge>
			</cac:Price>
			<cac:Item>
				<cbc:Description>Brown sauce - long description</cbc:Description>
				<cbc:Name>Brown sauce</cbc:Name>
				<cac:SellersItemIdentification>
					<cbc:ID>SN-33</cbc:ID>
				</cac:SellersItemIdentification>
				<cac:StandardItemIdentification>
					<cbc:ID schemeID="0160">7400000001234</cbc:ID>
				</cac:StandardItemIdentification>
				<cac:ItemSpecificationDocumentReference>
					<cbc:ID>147852</cbc:ID>
					<cbc:DocumentTypeCode>TRADE_ITEM_DESCRIPTION</cbc:DocumentTypeCode>
					<cbc:DocumentType>Timesheet</cbc:DocumentType>
					<cac:Attachment>
						<cbc:EmbeddedDocumentBinaryObject mimeCode="image/tiff" filename="hours-spend.csv">UjBsR09EbGhjZ0dTQUxNQUFBUUNBRU1tQ1p0dU1GUXhEUzhi</cbc:EmbeddedDocumentBinaryObject>
						<cac:ExternalReference>
							<cbc:URI>http://www.example.com/index.html</cbc:URI>
						</cac:ExternalReference>
					</cac:Attachment>
				</cac:ItemSpecificationDocumentReference>
				<cac:CommodityClassification>
					<cbc:ItemClassificationCode listID="MP" listVersionID="19.0501">12345678</cbc:ItemClassificationCode>
				</cac:CommodityClassification>
				<cac:TransactionConditions>
					<cbc:ActionCode>CT</cbc:ActionCode>
				</cac:TransactionConditions>
				<cac:ClassifiedTaxCategory>
					<cbc:ID>S</cbc:ID>
					<cbc:Percent>25</cbc:Percent>
					<cac:TaxScheme>
						<cbc:ID>VAT</cbc:ID>
					</cac:TaxScheme>
				</cac:ClassifiedTaxCategory>
				<cac:AdditionalItemProperty>
					<cbc:Name>Length</cbc:Name>
					<cbc:NameCode listID="NN">test</cbc:NameCode>
					<cbc:Value>30 mm</cbc:Value>
					
				</cac:AdditionalItemProperty>
				<cac:Certificate>
					<cbc:ID>EU EcoLabel</cbc:ID>
					<cbc:CertificateTypeCode>NA</cbc:CertificateTypeCode>
					<cbc:CertificateType>Environmental</cbc:CertificateType>
					<cbc:Remarks>Item labl value</cbc:Remarks>
					<cac:IssuerParty>
						<cac:PartyName>
							<cbc:Name>Issuer party name</cbc:Name>
						</cac:PartyName>
					</cac:IssuerParty>
					<cac:DocumentReference>
						<cbc:ID>http://www.label.eu/test/</cbc:ID>
					</cac:DocumentReference>
				</cac:Certificate>
			</cac:Item>
		</cac:LineItem>
	</cac:OrderLine>
	<cac:OrderLine>
		<cac:LineItem>
			<cbc:ID>2</cbc:ID>
			<cbc:Quantity unitCode="C62">1</cbc:Quantity>
			<cbc:LineExtensionAmount currencyID="EUR">10</cbc:LineExtensionAmount>
			<cac:Price>
				<cbc:PriceAmount currencyID="EUR">10.00</cbc:PriceAmount>
			</cac:Price>
			<cac:Item>
				<cbc:Name>White sauce</cbc:Name>
				<cac:SellersItemIdentification>
					<cbc:ID>SN-34</cbc:ID>
				</cac:SellersItemIdentification>
			</cac:Item>
		</cac:LineItem>
	</cac:OrderLine>
</OrderResponse>
