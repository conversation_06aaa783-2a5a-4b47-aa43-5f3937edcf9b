<?xml version="1.0" encoding="UTF-8"?>
<DespatchAdvice xmlns="urn:oasis:names:specification:ubl:schema:xsd:DespatchAdvice-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
	<cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:despatch_advice:3</cbc:CustomizationID>
	<cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:despatch_advice:3</cbc:ProfileID>
	<cbc:ID>1234</cbc:ID>
	<cbc:IssueDate>2019-03-15</cbc:IssueDate>
	<cbc:IssueTime>08:00:00</cbc:IssueTime>
	<cbc:Note>Use Case 4 Despatch with weight and/or volume based articles (ie vegetables, meat)</cbc:Note>
	<cac:OrderReference>
		<cbc:ID>4321</cbc:ID>
	</cac:OrderReference>
	<cac:DespatchSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">7385000000124</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7385000000124</cbc:ID>
			</cac:PartyIdentification>
			<!--			<cac:PartyName>
				<cbc:Name>Leverantören</cbc:Name>
			</cac:PartyName> -->
			<cac:PostalAddress>
				<cbc:StreetName>Sellerstreet</cbc:StreetName>
				<cbc:CityName>Sstaden</cbc:CityName>
				<cbc:PostalZone>12355</cbc:PostalZone>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Consortial</cbc:RegistrationName>
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:Name>John</cbc:Name>
				<cbc:Telephone>123456789</cbc:Telephone>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
	</cac:DespatchSupplierParty>
	<cac:DeliveryCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">7398000000124</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7390000435951</cbc:ID>
			</cac:PartyIdentification>
			<!--			<cac:PartyName>
				<cbc:Name>Leveransplatsen</cbc:Name>
			</cac:PartyName> -->
			<cac:PostalAddress>
				<cbc:StreetName>Leveransgatan 1</cbc:StreetName>
				<cbc:AdditionalStreetName>Lastkaj 2</cbc:AdditionalStreetName>
				<cbc:CityName>Kstaden</cbc:CityName>
				<cbc:PostalZone>12345</cbc:PostalZone>
				<cac:AddressLine>
					<cbc:Line>Lastkajen</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>IYT Corporation</cbc:RegistrationName>
			</cac:PartyLegalEntity>
		</cac:Party>
		<cac:DeliveryContact>
			<cbc:Name>Tony Erwing</cbc:Name>
			<cbc:Telephone>01272653214</cbc:Telephone>
			<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
		</cac:DeliveryContact>
	</cac:DeliveryCustomerParty>
	<cac:BuyerCustomerParty>
		<cac:Party>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7390000400003</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Köparen</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>Köpargatan 1</cbc:StreetName>
				<cbc:AdditionalStreetName>Hus A</cbc:AdditionalStreetName>
				<cbc:CityName>Kstaden</cbc:CityName>
				<cbc:PostalZone>12345</cbc:PostalZone>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
		</cac:Party>
	</cac:BuyerCustomerParty>
	<cac:OriginatorCustomerParty>
		<cac:Party>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7390000435968</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Godsmottagaren</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>Leveransgatan 1</cbc:StreetName>
				<cbc:AdditionalStreetName>Avdelning 2</cbc:AdditionalStreetName>
				<cbc:CityName>Kstaden</cbc:CityName>
				<cbc:PostalZone>12345</cbc:PostalZone>
				<cac:AddressLine>
					<cbc:Line>Rum 7</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
		</cac:Party>
	</cac:OriginatorCustomerParty>
	<cac:Shipment>
		<cbc:ID>NA</cbc:ID>
		<cbc:GrossWeightMeasure unitCode="KGM">15</cbc:GrossWeightMeasure>
		<cac:Delivery>
			<cac:EstimatedDeliveryPeriod>
				<cbc:StartDate>2019-03-15</cbc:StartDate>
				<cbc:StartTime>08:00:00</cbc:StartTime>
			</cac:EstimatedDeliveryPeriod>
		</cac:Delivery>
	</cac:Shipment>
	<cac:DespatchLine>
		<cbc:ID>1</cbc:ID>
		<cbc:DeliveredQuantity unitCode="KGM">4.25</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>1</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Cheese app 1 kg</cbc:Name>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">07311104114566</cbc:ID>
			</cac:StandardItemIdentification>
			<cac:ItemInstance>
				<cbc:BestBeforeDate>2019-05-01</cbc:BestBeforeDate>
			</cac:ItemInstance>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID>NA</cbc:ID>
			<cac:TransportHandlingUnit>
				<cbc:ID>173111000000000013</cbc:ID>
				<cbc:TransportHandlingUnitTypeCode>SW</cbc:TransportHandlingUnitTypeCode>
				<cac:MeasurementDimension>
					<cbc:AttributeID>AAB</cbc:AttributeID>
					<cbc:Measure unitCode="KGM">4.50</cbc:Measure>
				</cac:MeasurementDimension>
			</cac:TransportHandlingUnit>
		</cac:Shipment>
	</cac:DespatchLine>
	<cac:DespatchLine>
		<cbc:ID>2</cbc:ID>
		<cbc:DeliveredQuantity unitCode="KGM">5.97</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>2</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Bananas</cbc:Name>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">07311100000030</cbc:ID>
			</cac:StandardItemIdentification>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID>NA</cbc:ID>
			<cac:TransportHandlingUnit>
				<cbc:ID>173111000000000020</cbc:ID>
				<cbc:TransportHandlingUnitTypeCode>CT</cbc:TransportHandlingUnitTypeCode>
				<cac:MeasurementDimension>
					<cbc:AttributeID>AAB</cbc:AttributeID>
					<cbc:Measure unitCode="KGM">6.07</cbc:Measure>
				</cac:MeasurementDimension>
			</cac:TransportHandlingUnit>
		</cac:Shipment>
	</cac:DespatchLine>
	<cac:DespatchLine>
		<cbc:ID>3</cbc:ID>
		<cbc:DeliveredQuantity unitCode="EA">8</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>3</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Nasal Drops</cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID>87774744</cbc:ID>
			</cac:SellersItemIdentification>
			<cac:ItemInstance>
				<cac:LotIdentification>
					<cbc:LotNumberID>898A123</cbc:LotNumberID>
					<cbc:ExpiryDate>2021-07-01</cbc:ExpiryDate>
				</cac:LotIdentification>
			</cac:ItemInstance>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID>NA</cbc:ID>
			<cac:TransportHandlingUnit>
				<cbc:ID>173111000000000037</cbc:ID>
				<cbc:TransportHandlingUnitTypeCode>PA</cbc:TransportHandlingUnitTypeCode>
				<cac:MeasurementDimension>
					<cbc:AttributeID>AAB</cbc:AttributeID>
					<cbc:Measure unitCode="KGM">1.00</cbc:Measure>
				</cac:MeasurementDimension>
			</cac:TransportHandlingUnit>
		</cac:Shipment>
	</cac:DespatchLine>
	<cac:DespatchLine>
		<cbc:ID>4</cbc:ID>
		<cbc:DeliveredQuantity unitCode="EA">3</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>5</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Medical device 010120789</cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID>010120789</cbc:ID>
			</cac:SellersItemIdentification>
			<cac:ItemInstance>
				<cbc:SerialID>OR250RHZ444</cbc:SerialID>
			</cac:ItemInstance>
			<cac:ItemInstance>
				<cbc:SerialID>OR250RHZ4445</cbc:SerialID>
			</cac:ItemInstance>
			<cac:ItemInstance>
				<cbc:SerialID>OR250RHZ4446</cbc:SerialID>
			</cac:ItemInstance>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID>NA</cbc:ID>
			<cac:TransportHandlingUnit>
				<cbc:ID>173111000000000044</cbc:ID>
				<cbc:TransportHandlingUnitTypeCode>BX</cbc:TransportHandlingUnitTypeCode>
				<cac:MeasurementDimension>
					<cbc:AttributeID>AAB</cbc:AttributeID>
					<cbc:Measure unitCode="KGM">2.50</cbc:Measure>
				</cac:MeasurementDimension>
			</cac:TransportHandlingUnit>
		</cac:Shipment>
	</cac:DespatchLine>
</DespatchAdvice>
