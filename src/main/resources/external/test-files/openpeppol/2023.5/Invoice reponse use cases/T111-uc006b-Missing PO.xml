<?xml version="1.0" encoding="UTF-8"?>
<!--
                Content:
                This file contains a PEPPOL Invoice Message Response, profile 63.
				It demonstrates Use Case 6a — Invoice is under query because of wrong or missing information. 
				
				The use case assumes missing purchase order reference and proposes that the reference should be PO0001, the invoice is put under query 2 days after it was issued.
				
                Errors:
                None
                
                Warnings:
                None
				
-->
<ApplicationResponse xmlns="urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:invoice_response:3</cbc:CustomizationID>
	<cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:invoice_response:3</cbc:ProfileID>
	<cbc:ID>imrid001</cbc:ID>
	<cbc:IssueDate>2017-12-01</cbc:IssueDate>
	<cac:SenderParty>
		<cbc:EndpointID schemeID="0196">6963495890</cbc:EndpointID>
		<cac:PartyLegalEntity>
			<cbc:RegistrationName>Buyer organization</cbc:RegistrationName>
		</cac:PartyLegalEntity>
	</cac:SenderParty>
	<cac:ReceiverParty>
		<cbc:EndpointID schemeID="0196">6841569459</cbc:EndpointID>
		<cac:PartyLegalEntity>
			<cbc:RegistrationName>Seller company</cbc:RegistrationName>
		</cac:PartyLegalEntity>
	</cac:ReceiverParty>
	<cac:DocumentResponse>
		<cac:Response>
			<cbc:ResponseCode listID="UNCL4343OpSubset">UQ</cbc:ResponseCode>
			<cbc:EffectiveDate>2017-12-02</cbc:EffectiveDate>
			<cac:Status>
				<cbc:StatusReasonCode listID="OPStatusReason">REF</cbc:StatusReasonCode>
				<cac:Condition>
					<cbc:AttributeID>BT-13</cbc:AttributeID>
					<cbc:Description>PO0001</cbc:Description>
				</cac:Condition>
			</cac:Status>
			<cac:Status>
				<cbc:StatusReasonCode listID="OPStatusAction">PIN</cbc:StatusReasonCode>
			</cac:Status>
		</cac:Response>
		<cac:DocumentReference>
			<cbc:ID>inv021</cbc:ID>
			<cbc:DocumentTypeCode listID="UNCL1001">380</cbc:DocumentTypeCode>
		</cac:DocumentReference>
	</cac:DocumentResponse>
</ApplicationResponse>
