<?xml version="1.0" encoding="UTF-8"?>
<!--
                Content:
                This file contains a PEPPOL Invoice Message Response, profile 63.
				It demonstrates Use Case 6c — Invoice is under query because of wrong detail. Partial credit note is requested. 
				
				Use case demonstrates how line level corrections are given with textual notes.
				
                Errors:
                None
                
                Warnings:
                None
				
-->
<ApplicationResponse xmlns="urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:invoice_response:3</cbc:CustomizationID>
	<cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:invoice_response:3</cbc:ProfileID>
	<cbc:ID>imrid001</cbc:ID>
	<cbc:IssueDate>2017-12-01</cbc:IssueDate>
	<cac:SenderParty>
		<cbc:EndpointID schemeID="0196">6963495890</cbc:EndpointID>
		<cac:PartyLegalEntity>
			<cbc:RegistrationName>Buyer organization</cbc:RegistrationName>
		</cac:PartyLegalEntity>
	</cac:SenderParty>
	<cac:ReceiverParty>
		<cbc:EndpointID schemeID="0196">6841569459</cbc:EndpointID>
		<cac:PartyLegalEntity>
			<cbc:RegistrationName>Seller company</cbc:RegistrationName>
		</cac:PartyLegalEntity>
	</cac:ReceiverParty>
	<cac:DocumentResponse>
		<cac:Response>
			<cbc:ResponseCode listID="UNCL4343OpSubset">UQ</cbc:ResponseCode>
			<cac:Status>
				<cbc:StatusReasonCode listID="OPStatusReason">DEL</cbc:StatusReasonCode>
				<cbc:StatusReason>Delivered quantity for line number 1 was 2 units but invoiced quantity is 5 units. Send credit note for 3 unit.</cbc:StatusReason>
			</cac:Status>
			<cac:Status>
				<cbc:StatusReasonCode listID="OPStatusAction">CNP</cbc:StatusReasonCode>
			</cac:Status>
		</cac:Response>
		<cac:DocumentReference>
			<cbc:ID>inv021</cbc:ID>
			<cbc:DocumentTypeCode listID="UNCL1001">380</cbc:DocumentTypeCode>
		</cac:DocumentReference>
	</cac:DocumentResponse>
</ApplicationResponse>
