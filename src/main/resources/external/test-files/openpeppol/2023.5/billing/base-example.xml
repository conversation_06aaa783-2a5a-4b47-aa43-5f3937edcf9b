<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
    xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0</cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>Snippet1</cbc:ID>
    <cbc:IssueDate>2017-11-13</cbc:IssueDate>
    <cbc:DueDate>2017-12-01</cbc:DueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
    <cbc:DocumentCurrencyCode>EUR</cbc:DocumentCurrencyCode>
    <cbc:AccountingCost>4025:123:4343</cbc:AccountingCost>
    <cbc:BuyerReference>0150abc</cbc:BuyerReference>
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0088">9482348239847239874</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID>********</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>SupplierTradingName Ltd.</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Main street 1</cbc:StreetName>
                <cbc:AdditionalStreetName>Postbox 123</cbc:AdditionalStreetName>
                <cbc:CityName>London</cbc:CityName>
                <cbc:PostalZone>GB 123 EW</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>GB</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>GB1232434</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>SupplierOfficialName Ltd</cbc:RegistrationName>
                <cbc:CompanyID>GB983294</cbc:CompanyID>
            </cac:PartyLegalEntity>
        </cac:Party>
    </cac:AccountingSupplierParty>
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0002">FR23342</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID schemeID="0002">FR23342</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>BuyerTradingName AS</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Hovedgatan 32</cbc:StreetName>
                <cbc:AdditionalStreetName>Po box 878</cbc:AdditionalStreetName>
                <cbc:CityName>Stockholm</cbc:CityName>
                <cbc:PostalZone>456 34</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>SE</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>SE4598375937</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>Buyer Official Name</cbc:RegistrationName>
                <cbc:CompanyID schemeID="0183">***********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>Lisa Johnson</cbc:Name>
                <cbc:Telephone>********</cbc:Telephone>
                <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>
    <cac:Delivery>
        <cbc:ActualDeliveryDate>2017-11-01</cbc:ActualDeliveryDate>
        <cac:DeliveryLocation>
            <cbc:ID schemeID="0088">****************</cbc:ID>
            <cac:Address>
                <cbc:StreetName>Delivery street 2</cbc:StreetName>
                <cbc:AdditionalStreetName>Building 56</cbc:AdditionalStreetName>
                <cbc:CityName>Stockholm</cbc:CityName>
                <cbc:PostalZone>21234</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>SE</cbc:IdentificationCode>
                </cac:Country>
            </cac:Address>
        </cac:DeliveryLocation>
        <cac:DeliveryParty>
            <cac:PartyName>
                <cbc:Name>Delivery party Name</cbc:Name>
            </cac:PartyName>
        </cac:DeliveryParty>
    </cac:Delivery>
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode name="Credit transfer">30</cbc:PaymentMeansCode>
        <cbc:PaymentID>Snippet1</cbc:PaymentID>
        <cac:PayeeFinancialAccount>
            <cbc:ID>IBAN32423940</cbc:ID>
            <cbc:Name>AccountName</cbc:Name>
            <cac:FinancialInstitutionBranch>
                <cbc:ID>BIC324098</cbc:ID>
            </cac:FinancialInstitutionBranch>
        </cac:PayeeFinancialAccount>
    </cac:PaymentMeans>
    <cac:PaymentTerms>
        <cbc:Note>Payment within 10 days, 2% discount</cbc:Note>
    </cac:PaymentTerms>
        <cac:AllowanceCharge>
            <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReason>Insurance</cbc:AllowanceChargeReason>
            <cbc:Amount currencyID="EUR">25</cbc:Amount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>25.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:AllowanceCharge>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="EUR">331.25</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="EUR">1325</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="EUR">331.25</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>25.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="EUR">1300</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="EUR">1325</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="EUR">1656.25</cbc:TaxInclusiveAmount>
        <cbc:ChargeTotalAmount currencyID="EUR">25</cbc:ChargeTotalAmount>
        <cbc:PayableAmount currencyID="EUR">1656.25</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    
<cac:InvoiceLine>
        <cbc:ID>1</cbc:ID>
    <cbc:InvoicedQuantity unitCode="DAY">7</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID= "EUR">2800</cbc:LineExtensionAmount>
        <cbc:AccountingCost>Konteringsstreng</cbc:AccountingCost>
       <cac:OrderLineReference>
            <cbc:LineID>123</cbc:LineID>
        </cac:OrderLineReference>
    <cac:Item>
            <cbc:Description>Description of item</cbc:Description>
            <cbc:Name>item name</cbc:Name>
            <cac:StandardItemIdentification>
                <cbc:ID schemeID="0088">**************</cbc:ID>
            </cac:StandardItemIdentification>
            <cac:OriginCountry>
                <cbc:IdentificationCode>NO</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
                <cbc:ItemClassificationCode listID="SRV">********</cbc:ItemClassificationCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>25.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
    <cac:Price>
        <cbc:PriceAmount currencyID="EUR">400</cbc:PriceAmount>
    </cac:Price>
    </cac:InvoiceLine>
<cac:InvoiceLine>
    <cbc:ID>2</cbc:ID>
    <cbc:InvoicedQuantity unitCode="DAY">-3</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="EUR">-1500</cbc:LineExtensionAmount>
    <cac:OrderLineReference>
        <cbc:LineID>123</cbc:LineID>
    </cac:OrderLineReference>
    <cac:Item>
        <cbc:Description>Description 2</cbc:Description>
        <cbc:Name>item name 2</cbc:Name>
        <cac:StandardItemIdentification>
            <cbc:ID schemeID="0088">**************</cbc:ID>
        </cac:StandardItemIdentification>
        <cac:OriginCountry>
            <cbc:IdentificationCode>NO</cbc:IdentificationCode>
        </cac:OriginCountry>
        <cac:CommodityClassification>
            <cbc:ItemClassificationCode listID="SRV">********</cbc:ItemClassificationCode>
        </cac:CommodityClassification>
        <cac:ClassifiedTaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>25.0</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
        <cbc:PriceAmount currencyID="EUR">500</cbc:PriceAmount>
    </cac:Price>
</cac:InvoiceLine>
</Invoice>
