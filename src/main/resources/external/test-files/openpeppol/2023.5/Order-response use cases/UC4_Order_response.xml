<?xml version="1.0" encoding="UTF-8"?>
<OrderResponse xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="urn:oasis:names:specification:ubl:schema:xsd:OrderResponse-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
	<cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:order_response:3</cbc:CustomizationID>
	<cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:ordering:3</cbc:ProfileID>
	<cbc:ID>4552</cbc:ID>
	<cbc:IssueDate>2013-07-01</cbc:IssueDate>
	<cbc:IssueTime>06:10:10</cbc:IssueTime>
	<cbc:OrderResponseCode>CA</cbc:OrderResponseCode>
	<cbc:Note>Response message with amendments in the details</cbc:Note>
	<cbc:DocumentCurrencyCode listID="ISO4217">EUR</cbc:DocumentCurrencyCode>
	<cbc:CustomerReference>Your ref</cbc:CustomerReference>
	<cac:OrderReference>
		<cbc:ID>5</cbc:ID>
	</cac:OrderReference>
	<cac:SellerSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0192">987654325</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0192">987654325</cbc:ID>
			</cac:PartyIdentification>
		</cac:Party>
	</cac:SellerSupplierParty>
	<cac:BuyerCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">7300010000001</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7300010000001</cbc:ID>
			</cac:PartyIdentification>
		</cac:Party>
	</cac:BuyerCustomerParty>
	<cac:Delivery>
		<cac:PromisedDeliveryPeriod>
			<cbc:StartDate>2013-07-15</cbc:StartDate>
			<cbc:EndDate>2013-07-16</cbc:EndDate>
		</cac:PromisedDeliveryPeriod>
	</cac:Delivery>
	<cac:OrderLine>
		<cac:LineItem>
			<cbc:ID>1</cbc:ID>
			<cbc:Note>with changes (price)</cbc:Note>
			<cbc:LineStatusCode>3</cbc:LineStatusCode>
			<cbc:Quantity unitCode="NAR" unitCodeListID="UNECERec20">500</cbc:Quantity>
			<cac:Delivery>
				<cac:PromisedDeliveryPeriod>
					<cbc:StartDate>2013-07-15</cbc:StartDate>
					<cbc:EndDate>2013-07-16</cbc:EndDate>
				</cac:PromisedDeliveryPeriod>
			</cac:Delivery>
			<cac:Price>
				<cbc:PriceAmount currencyID="EUR" >0.9</cbc:PriceAmount>
				<cbc:BaseQuantity unitCode="NAR" unitCodeListID="UNECERec20">10</cbc:BaseQuantity>
			</cac:Price>
			<cac:Item>
				<cbc:Name>Snow shovel</cbc:Name>
				<cac:SellersItemIdentification>
					<cbc:ID>SN-33</cbc:ID>
				</cac:SellersItemIdentification>
				<cac:StandardItemIdentification>
					<cbc:ID schemeID="0160">09876543211234</cbc:ID>
				</cac:StandardItemIdentification>
			</cac:Item>
		</cac:LineItem>
		<cac:OrderLineReference>
			<cbc:LineID>1</cbc:LineID>
		</cac:OrderLineReference>
	</cac:OrderLine>
</OrderResponse>
