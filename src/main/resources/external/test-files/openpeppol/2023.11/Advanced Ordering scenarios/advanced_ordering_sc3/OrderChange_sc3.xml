<?xml version="1.0" encoding="UTF-8"?>
<OrderChange
    xmlns="urn:oasis:names:specification:ubl:schema:xsd:OrderChange-2"
    xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
    xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:order_change:3</cbc:CustomizationID>
  <cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:advanced_ordering:3</cbc:ProfileID>
  <cbc:ID>Change-3</cbc:ID>
  <cbc:IssueDate>2022-02-10</cbc:IssueDate>
  <cbc:SequenceNumberID>2</cbc:SequenceNumberID>
  <cbc:Note>Changes according to Order reponse</cbc:Note>
  <cbc:DocumentCurrencyCode>EUR</cbc:DocumentCurrencyCode>
  <cac:ValidityPeriod>
    <cbc:EndDate>2022-03-01</cbc:EndDate>
  </cac:ValidityPeriod>
  <cac:OrderReference>
    <cbc:ID>Order-1</cbc:ID>
  </cac:OrderReference>
  <cac:BuyerCustomerParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0088">7300010000001</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0007">5541277711</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>City Hospital</cbc:Name>
      </cac:PartyName>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>City Hospital 345433</cbc:RegistrationName>
        <cbc:CompanyID schemeID="0007">5541277711</cbc:CompanyID>
        <cac:RegistrationAddress>
          <cbc:CityName>Eurocity</cbc:CityName>
          <cac:Country>
            <cbc:IdentificationCode>SE</cbc:IdentificationCode>
          </cac:Country>
        </cac:RegistrationAddress>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>Martin Foggerty</cbc:Name>
        <cbc:Telephone>+46555785488</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:BuyerCustomerParty>
  <cac:SellerSupplierParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0088">7302347231110</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0007">5546577799</cbc:ID>
      </cac:PartyIdentification>
      <cac:PostalAddress>
        <cbc:StreetName>Harbour street</cbc:StreetName>
        <cbc:AdditionalStreetName>Dock 45</cbc:AdditionalStreetName>
        <cbc:CityName>Bergen</cbc:CityName>
        <cac:Country>
          <cbc:IdentificationCode>NO</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>     
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>The Supplier AB</cbc:RegistrationName>
      </cac:PartyLegalEntity>
    </cac:Party>
  </cac:SellerSupplierParty>
  <cac:Delivery>
    <cac:DeliveryLocation>
      <cac:Address>
        <cbc:StreetName>Lower street 5</cbc:StreetName>
        <cbc:AdditionalStreetName>Reception</cbc:AdditionalStreetName>
        <cbc:CityName>Stockholm</cbc:CityName>
        <cbc:PostalZone>11120</cbc:PostalZone>
        <cac:AddressLine>
			<cbc:Line>Right</cbc:Line>
        </cac:AddressLine>
        <cac:Country>
          <cbc:IdentificationCode>SE</cbc:IdentificationCode>
        </cac:Country>
      </cac:Address>
    </cac:DeliveryLocation>
    <cac:DeliveryParty>
      <cac:PartyName>
        <cbc:Name>Hospital Tourist Department</cbc:Name>
      </cac:PartyName>
      <cac:Contact>
        <cbc:Name>John</cbc:Name>
        <cbc:Telephone>+465558877523</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:DeliveryParty>
  </cac:Delivery>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="EUR">100</cbc:TaxAmount>
  </cac:TaxTotal>
  <cac:AnticipatedMonetaryTotal>
		<cbc:LineExtensionAmount currencyID="EUR">500</cbc:LineExtensionAmount>
		<cbc:TaxExclusiveAmount currencyID="EUR">500</cbc:TaxExclusiveAmount>
		<cbc:TaxInclusiveAmount currencyID="EUR">600</cbc:TaxInclusiveAmount>
		<cbc:PayableAmount currencyID="EUR">600</cbc:PayableAmount>
	</cac:AnticipatedMonetaryTotal>
  <cac:OrderLine>
    <cac:LineItem>
      <cbc:ID>1</cbc:ID>
      <cbc:LineStatusCode>3</cbc:LineStatusCode>
      <cbc:Quantity unitCode="NAR">5</cbc:Quantity>
      <cbc:LineExtensionAmount currencyID="EUR">200</cbc:LineExtensionAmount>
      <cac:Delivery>
        <cac:RequestedDeliveryPeriod>
          <cbc:StartDate>2022-03-01</cbc:StartDate>
          <cbc:EndDate>2022-04-01</cbc:EndDate>
        </cac:RequestedDeliveryPeriod>
      </cac:Delivery>
      <cac:Price>
        <cbc:PriceAmount currencyID="EUR">40</cbc:PriceAmount>
      </cac:Price>
      <cac:Item>
        <cbc:Name>Item 1</cbc:Name>
        <cac:ClassifiedTaxCategory>
          <cbc:ID>S</cbc:ID>
          <cbc:Percent>20</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
          </cac:TaxScheme>
        </cac:ClassifiedTaxCategory>
      </cac:Item>
    </cac:LineItem>
  </cac:OrderLine>
  <cac:OrderLine>
    <cac:LineItem>
      <cbc:ID>2</cbc:ID>
      <cbc:LineStatusCode>3</cbc:LineStatusCode>
      <cbc:Quantity unitCode="NAR">50</cbc:Quantity>
      <cbc:LineExtensionAmount currencyID="EUR">300</cbc:LineExtensionAmount>
      <cac:Price>
        <cbc:PriceAmount currencyID="EUR">6</cbc:PriceAmount>
      </cac:Price>
      <cac:Item>
        <cbc:Name>Item 2</cbc:Name>
        <cac:ClassifiedTaxCategory>
          <cbc:ID>S</cbc:ID>
          <cbc:Percent>20</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
          </cac:TaxScheme>
        </cac:ClassifiedTaxCategory>
      </cac:Item>
    </cac:LineItem>
  </cac:OrderLine>
</OrderChange>
