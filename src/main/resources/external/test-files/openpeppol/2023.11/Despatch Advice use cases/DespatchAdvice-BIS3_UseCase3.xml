<?xml version="1.0" encoding="UTF-8"?>
<DespatchAdvice xmlns="urn:oasis:names:specification:ubl:schema:xsd:DespatchAdvice-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
	<cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:despatch_advice:3</cbc:CustomizationID>
	<cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:despatch_advice:3</cbc:ProfileID>
	<cbc:ID>1238</cbc:ID>
	<cbc:IssueDate>2019-03-15</cbc:IssueDate>
	<cbc:IssueTime>08:00:00</cbc:IssueTime>
	<cbc:Note>Goods are not to be left unattended</cbc:Note>
	<cac:OrderReference>
		<cbc:ID>4321</cbc:ID>
	</cac:OrderReference>
	<cac:DespatchSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">7385000000124</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7385000000124</cbc:ID>
			</cac:PartyIdentification>
			<!--			<cac:PartyName>
				<cbc:Name>Leverantören</cbc:Name>
			</cac:PartyName> -->
			<cac:PostalAddress>
				<cbc:StreetName>Sellerstreet</cbc:StreetName>
				<cbc:CityName>Sstaden</cbc:CityName>
				<cbc:PostalZone>12355</cbc:PostalZone>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Consortial</cbc:RegistrationName>
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:Name>John</cbc:Name>
				<cbc:Telephone>123456789</cbc:Telephone>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
	</cac:DespatchSupplierParty>
	<cac:DeliveryCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">7398000000124</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7390000435951</cbc:ID>
			</cac:PartyIdentification>
			<!--			<cac:PartyName>
				<cbc:Name>Leveransplatsen</cbc:Name>
			</cac:PartyName> -->
			<cac:PostalAddress>
				<cbc:StreetName>Leveransgatan 1</cbc:StreetName>
				<cbc:AdditionalStreetName>Lastkaj 2</cbc:AdditionalStreetName>
				<cbc:CityName>Kstaden</cbc:CityName>
				<cbc:PostalZone>12345</cbc:PostalZone>
				<cac:AddressLine>
					<cbc:Line>Lastkajen</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>IYT Corporation</cbc:RegistrationName>
			</cac:PartyLegalEntity>
		</cac:Party>
		<cac:DeliveryContact>
			<cbc:Name>Tony Erwing</cbc:Name>
			<cbc:Telephone>01272653214</cbc:Telephone>
			<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
		</cac:DeliveryContact>
	</cac:DeliveryCustomerParty>
	<cac:BuyerCustomerParty>
		<cac:Party>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7390000400003</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Köparen</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>Köpargatan 1</cbc:StreetName>
				<cbc:AdditionalStreetName>Hus A</cbc:AdditionalStreetName>
				<cbc:CityName>Kstaden</cbc:CityName>
				<cbc:PostalZone>12345</cbc:PostalZone>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
		</cac:Party>
	</cac:BuyerCustomerParty>
	<cac:OriginatorCustomerParty>
		<cac:Party>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7390000435968</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Godsmottagaren</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>Leveransgatan 1</cbc:StreetName>
				<cbc:AdditionalStreetName>Avdelning 2</cbc:AdditionalStreetName>
				<cbc:CityName>Kstaden</cbc:CityName>
				<cbc:PostalZone>12345</cbc:PostalZone>
				<cac:AddressLine>
					<cbc:Line>Rum 7</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
		</cac:Party>
	</cac:OriginatorCustomerParty>
	<cac:Shipment>
		<cbc:ID>57098761234567890</cbc:ID>
		<cbc:Information>Free text information relating to the Shipment</cbc:Information>
		<cbc:GrossWeightMeasure unitCode="KGM">23.00</cbc:GrossWeightMeasure>
		<cbc:GrossVolumeMeasure unitCode="MTQ">27.00</cbc:GrossVolumeMeasure>
		<cac:Consignment>
			<cbc:ID>570987698767654567898767876765</cbc:ID>
			<cac:CarrierParty>
				<cac:PartyName>
					<cbc:Name>Swift and Sure Freight Co Ltd</cbc:Name>
				</cac:PartyName>
			</cac:CarrierParty>
		</cac:Consignment>
		<cac:Delivery>
			<cac:EstimatedDeliveryPeriod>
				<cbc:StartDate>2019-03-15</cbc:StartDate>
				<cbc:StartTime>08:00:00</cbc:StartTime>
				<cbc:EndDate>2019-03-16</cbc:EndDate>
				<cbc:EndTime>12:00:00</cbc:EndTime>
			</cac:EstimatedDeliveryPeriod>
			<cac:Despatch>
				<cbc:ActualDespatchDate>2019-03-13</cbc:ActualDespatchDate>
				<cbc:ActualDespatchTime>08:00:00</cbc:ActualDespatchTime>
			</cac:Despatch>
		</cac:Delivery>
	</cac:Shipment>
	<cac:DespatchLine>
		<cbc:ID>1</cbc:ID>
		<cbc:Note>Free text information relating to the despatch line</cbc:Note>
		<cbc:DeliveredQuantity unitCode="EA">10</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>1</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Wondercure medicare kit</cbc:Name>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">05702938473625</cbc:ID>
			</cac:StandardItemIdentification>
			<cac:ItemInstance>
				<cbc:SerialID>1234567</cbc:SerialID>
				<cac:LotIdentification>
					<cbc:LotNumberID>898A123</cbc:LotNumberID>
					<cbc:ExpiryDate>2021-07-01</cbc:ExpiryDate>
				</cac:LotIdentification>
			</cac:ItemInstance>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID>NA</cbc:ID>
			<cac:TransportHandlingUnit>
				<cbc:ID>340123450000000014</cbc:ID>
				<cbc:TransportHandlingUnitTypeCode>OE</cbc:TransportHandlingUnitTypeCode>
				<cac:MeasurementDimension>
					<cbc:AttributeID>AAB</cbc:AttributeID>
					<cbc:Measure unitCode="KGM">10.00</cbc:Measure>
				</cac:MeasurementDimension>
			</cac:TransportHandlingUnit>
		</cac:Shipment>
	</cac:DespatchLine>
	<cac:DespatchLine>
		<cbc:ID>2</cbc:ID>
		<cbc:Note>Free text information relating to the despatch line</cbc:Note>
		<cbc:DeliveredQuantity unitCode="EA">23</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>2</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Brake-a-leg Supersplint set</cbc:Name>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">7611104117051</cbc:ID>
			</cac:StandardItemIdentification>
			<cac:ItemInstance>
				<cac:LotIdentification>
					<cbc:LotNumberID>898A124</cbc:LotNumberID>
					<cbc:ExpiryDate>2021-07-01</cbc:ExpiryDate>
				</cac:LotIdentification>
			</cac:ItemInstance>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID>NA</cbc:ID>
			<cac:TransportHandlingUnit>
				<cbc:ID>340123450000000014</cbc:ID>
				<cbc:TransportHandlingUnitTypeCode>OE</cbc:TransportHandlingUnitTypeCode>
				<cac:MeasurementDimension>
					<cbc:AttributeID>AAB</cbc:AttributeID>
					<cbc:Measure unitCode="KGM">7.00</cbc:Measure>
				</cac:MeasurementDimension>
			</cac:TransportHandlingUnit>
		</cac:Shipment>
	</cac:DespatchLine>
	<cac:DespatchLine>
		<cbc:ID>3</cbc:ID>
		<cbc:Note>Free text information relating to the despatch line</cbc:Note>
		<cbc:DeliveredQuantity unitCode="EA">500</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>3</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Cough sirup</cbc:Name>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">08886765117054</cbc:ID>
				<cbc:ExtendedID>123</cbc:ExtendedID>
			</cac:StandardItemIdentification>
			<cac:ItemInstance>
				<cac:LotIdentification>
					<cbc:LotNumberID>898A128</cbc:LotNumberID>
					<cbc:ExpiryDate>2021-07-01</cbc:ExpiryDate>
				</cac:LotIdentification>
			</cac:ItemInstance>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID>NA</cbc:ID>
			<cac:TransportHandlingUnit>
				<cbc:ID>354123450000000106</cbc:ID>
				<cbc:TransportHandlingUnitTypeCode>OE</cbc:TransportHandlingUnitTypeCode>
				<cac:MeasurementDimension>
					<cbc:AttributeID>AAB</cbc:AttributeID>
					<cbc:Measure unitCode="KGM">7.00</cbc:Measure>
				</cac:MeasurementDimension>
			</cac:TransportHandlingUnit>
		</cac:Shipment>
	</cac:DespatchLine>
	<cac:DespatchLine>
		<cbc:ID>4</cbc:ID>
		<cbc:Note>Free text information relating to the despatch line</cbc:Note>
		<cbc:DeliveredQuantity unitCode="EA">12</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>4</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Tray, plastic</cbc:Name>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">057098987656543</cbc:ID>
			</cac:StandardItemIdentification>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID>NA</cbc:ID>
			<cac:TransportHandlingUnit>
				<cbc:ID>354123450000000106</cbc:ID>
				<cbc:TransportHandlingUnitTypeCode>OE</cbc:TransportHandlingUnitTypeCode>
				<cac:MeasurementDimension>
					<cbc:AttributeID>AAB</cbc:AttributeID>
					<cbc:Measure unitCode="KGM">7.00</cbc:Measure>
				</cac:MeasurementDimension>
			</cac:TransportHandlingUnit>
		</cac:Shipment>
	</cac:DespatchLine>
</DespatchAdvice>
