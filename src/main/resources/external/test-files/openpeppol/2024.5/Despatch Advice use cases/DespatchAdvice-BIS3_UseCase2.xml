<?xml version="1.0" encoding="UTF-8"?>
<DespatchAdvice xmlns="urn:oasis:names:specification:ubl:schema:xsd:DespatchAdvice-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
	<cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:despatch_advice:3</cbc:CustomizationID>
	<cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:despatch_advice:3</cbc:ProfileID>
	<cbc:ID>1236</cbc:ID>
	<cbc:IssueDate>2013-03-15</cbc:IssueDate>
	<cbc:IssueTime>08:00:00</cbc:IssueTime>
	<cbc:Note>Free text note relating to the Despatch Advice</cbc:Note>
	<cac:OrderReference>
		<cbc:ID>4321</cbc:ID>
	</cac:OrderReference>
	<cac:DespatchSupplierParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">7385000000124</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7385000000124</cbc:ID>
			</cac:PartyIdentification>
			<!--			<cac:PartyName>
				<cbc:Name>Leverantören</cbc:Name>
			</cac:PartyName> -->
			<cac:PostalAddress>
				<cbc:StreetName>Sellerstreet</cbc:StreetName>
				<cbc:CityName>Sstaden</cbc:CityName>
				<cbc:PostalZone>12355</cbc:PostalZone>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>Consortial</cbc:RegistrationName>
			</cac:PartyLegalEntity>
			<cac:Contact>
				<cbc:Name>John</cbc:Name>
				<cbc:Telephone>123456789</cbc:Telephone>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
	</cac:DespatchSupplierParty>
	<cac:DeliveryCustomerParty>
		<cac:Party>
			<cbc:EndpointID schemeID="0088">7398000000124</cbc:EndpointID>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7390000435951</cbc:ID>
			</cac:PartyIdentification>
			<!--			<cac:PartyName>
				<cbc:Name>Leveransplatsen</cbc:Name>
			</cac:PartyName> -->
			<cac:PostalAddress>
				<cbc:StreetName>Leveransgatan 1</cbc:StreetName>
				<cbc:AdditionalStreetName>Lastkaj 2</cbc:AdditionalStreetName>
				<cbc:CityName>Kstaden</cbc:CityName>
				<cbc:PostalZone>12345</cbc:PostalZone>
				<cac:AddressLine>
					<cbc:Line>Lastkajen</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyLegalEntity>
				<cbc:RegistrationName>IYT Corporation</cbc:RegistrationName>
			</cac:PartyLegalEntity>
		</cac:Party>
		<cac:DeliveryContact>
			<cbc:Name>Tony Erwing</cbc:Name>
			<cbc:Telephone>01272653214</cbc:Telephone>
			<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
		</cac:DeliveryContact>
	</cac:DeliveryCustomerParty>
	<cac:BuyerCustomerParty>
		<cac:Party>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7390000400003</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Köparen</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>Köpargatan 1</cbc:StreetName>
				<cbc:AdditionalStreetName>Hus A</cbc:AdditionalStreetName>
				<cbc:CityName>Kstaden</cbc:CityName>
				<cbc:PostalZone>12345</cbc:PostalZone>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
		</cac:Party>
	</cac:BuyerCustomerParty>
	<cac:OriginatorCustomerParty>
		<cac:Party>
			<cac:PartyIdentification>
				<cbc:ID schemeID="0088">7390000435968</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Godsmottagaren</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>Leveransgatan 1</cbc:StreetName>
				<cbc:AdditionalStreetName>Avdelning 2</cbc:AdditionalStreetName>
				<cbc:CityName>Kstaden</cbc:CityName>
				<cbc:PostalZone>12345</cbc:PostalZone>
				<cac:AddressLine>
					<cbc:Line>Rum 7</cbc:Line>
				</cac:AddressLine>
				<cac:Country>
					<cbc:IdentificationCode>SE</cbc:IdentificationCode>
				</cac:Country>
			</cac:PostalAddress>
		</cac:Party>
	</cac:OriginatorCustomerParty>
	<cac:Shipment>
		<cbc:ID>NA</cbc:ID>
		<cbc:Information>Free text information relating to the Shipment</cbc:Information>
		<cbc:GrossWeightMeasure unitCode="KGM">23</cbc:GrossWeightMeasure>
		<cbc:GrossVolumeMeasure unitCode="MTQ">27</cbc:GrossVolumeMeasure>
		<cac:Consignment>
			<cbc:ID>12345</cbc:ID>
			<cac:CarrierParty>
				<cac:PartyName>
					<cbc:Name>CarrierPart</cbc:Name>
				</cac:PartyName>
			</cac:CarrierParty>
		</cac:Consignment>
		<cac:Delivery>
			<cac:EstimatedDeliveryPeriod>
				<cbc:StartDate>2013-03-15</cbc:StartDate>
				<cbc:StartTime>08:00:00</cbc:StartTime>
				<cbc:EndDate>2013-03-16</cbc:EndDate>
				<cbc:EndTime>12:00:00</cbc:EndTime>
			</cac:EstimatedDeliveryPeriod>
			<cac:Despatch>
				<cbc:ActualDespatchDate>2013-03-13</cbc:ActualDespatchDate>
				<cbc:ActualDespatchTime>08:00:00</cbc:ActualDespatchTime>
			</cac:Despatch>
		</cac:Delivery>
	</cac:Shipment>
	<cac:DespatchLine>
		<!-- In this line 10 items were ordered and 10 items is delivered-->
		<cbc:ID>1</cbc:ID>
		<cbc:Note>Free text information relating to the despatch line</cbc:Note>
		<cbc:DeliveredQuantity unitCode="EA">10</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>1</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Item123</cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID>010120401</cbc:ID>
				<cbc:ExtendedID>123</cbc:ExtendedID>
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">7611104117056</cbc:ID>
				<cbc:ExtendedID>123</cbc:ExtendedID>
			</cac:StandardItemIdentification>
		</cac:Item>
	</cac:DespatchLine>
	<!-- In this line 10 items were ordered but 4 items cant be delivered in this despatch, but will be delivered in a later despatch-->
	<cac:DespatchLine>
		<cbc:ID>2</cbc:ID>
		<cbc:Note>Free text information relating to the despatch line</cbc:Note>
		<cbc:DeliveredQuantity unitCode="EA">6</cbc:DeliveredQuantity>
		<cbc:OutstandingQuantity unitCode="EA">4</cbc:OutstandingQuantity>
		<cbc:OutstandingReason>AV</cbc:OutstandingReason>
		<cac:OrderLineReference>
			<cbc:LineID>2</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Item456</cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID>010120409</cbc:ID>
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">7611104117054</cbc:ID>
			</cac:StandardItemIdentification>
		</cac:Item>
	</cac:DespatchLine>
	<!-- In this line 10 items is ordered and 6 the items can be delivered in this despatch. 4 will not be  delivered (notice the 0 in outstanding quantity)-->
	<cac:DespatchLine>
		<cbc:ID>3</cbc:ID>
		<cbc:Note>Free text information relating to the despatch line</cbc:Note>
		<cbc:DeliveredQuantity unitCode="EA">6</cbc:DeliveredQuantity>
		<cbc:OutstandingQuantity unitCode="EA">0</cbc:OutstandingQuantity>
		<cbc:OutstandingReason>AV</cbc:OutstandingReason>
		<cac:OrderLineReference>
			<cbc:LineID>3</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Item789</cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID>010120405</cbc:ID>
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">7611104117052</cbc:ID>
			</cac:StandardItemIdentification>
		</cac:Item>
	</cac:DespatchLine>
	<!-- In this line 10 items is ordered and 6 is delivered, 3 is outstanding to be delivered in a later despatch and 1 will implicitly not be delivered-->
	<cac:DespatchLine>
		<cbc:ID>4</cbc:ID>
		<cbc:Note>Free text information relating to the despatch line</cbc:Note>
		<cbc:DeliveredQuantity unitCode="EA">6</cbc:DeliveredQuantity>
		<cbc:OutstandingQuantity unitCode="EA">3</cbc:OutstandingQuantity>
		<cbc:OutstandingReason>AV</cbc:OutstandingReason>
		<cac:OrderLineReference>
			<cbc:LineID>4</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Item321</cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID>010120407</cbc:ID>
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">7611104117055</cbc:ID>
			</cac:StandardItemIdentification>
		</cac:Item>
	</cac:DespatchLine>
	<!-- In this line 10 items is ordered but 12 is delivered, this is a scenario with an Over shipment-->
	<cac:DespatchLine>
		<cbc:ID>5</cbc:ID>
		<cbc:Note>This line is contains an oversupply</cbc:Note>
		<cbc:DeliveredQuantity unitCode="EA">12</cbc:DeliveredQuantity>
		<cbc:OutstandingReason>WQ</cbc:OutstandingReason>
		<cac:OrderLineReference>
			<cbc:LineID>5</cbc:LineID>
			<cac:OrderReference>
				<cbc:ID>4321</cbc:ID>
			</cac:OrderReference>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Item654</cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID>010120408</cbc:ID>
			</cac:SellersItemIdentification>
			<cac:StandardItemIdentification>
				<cbc:ID schemeID="0160">7611104117051</cbc:ID>
			</cac:StandardItemIdentification>
		</cac:Item>
	</cac:DespatchLine>
</DespatchAdvice>
