<?xml version="1.0" encoding="UTF-8"?>
<OrderResponse xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns="urn:oasis:names:specification:ubl:schema:xsd:OrderResponse-2"
    xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
  <cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:order_response:3</cbc:CustomizationID>
  <cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:ordering:3</cbc:ProfileID>
  <cbc:ID>101</cbc:ID>
  <cbc:IssueDate>2019-10-01</cbc:IssueDate>
  <cbc:IssueTime>14:23:26</cbc:IssueTime>
  <cbc:OrderResponseCode>AP</cbc:OrderResponseCode>
  <cbc:DocumentCurrencyCode>SEK</cbc:DocumentCurrencyCode>
  <cac:OrderReference>
    <cbc:ID>5</cbc:ID>
  </cac:OrderReference>
  <cac:SellerSupplierParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0007">5546577791</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0007">5546577791</cbc:ID>
      </cac:PartyIdentification>
    </cac:Party>
  </cac:SellerSupplierParty>
   <cac:BuyerCustomerParty>
     <cac:Party>
       <cbc:EndpointID schemeID="0007">5541277710</cbc:EndpointID>
       <cac:PartyIdentification>
         <cbc:ID schemeID="0007">5541277710</cbc:ID>
       </cac:PartyIdentification>
     </cac:Party>
   </cac:BuyerCustomerParty>
  </OrderResponse>
