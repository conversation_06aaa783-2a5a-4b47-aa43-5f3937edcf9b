<?xml version="1.0" encoding="UTF-8"?>
<Order xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns="urn:oasis:names:specification:ubl:schema:xsd:Order-2"
    xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
  <cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:order:3</cbc:CustomizationID>
  <cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:order_only:3</cbc:ProfileID>
  <cbc:ID>5</cbc:ID>
  <cbc:IssueDate>2013-07-01</cbc:IssueDate>
  <cbc:IssueTime>05:10:10</cbc:IssueTime>
  <cbc:Note>Notes regarding the order</cbc:Note>
  <cbc:DocumentCurrencyCode  listID="ISO4217">EUR</cbc:DocumentCurrencyCode>
  <cbc:AccountingCost>MAFO</cbc:AccountingCost>
  <cac:ValidityPeriod>
    <cbc:EndDate>2013-07-30</cbc:EndDate>
  </cac:ValidityPeriod>
  <cac:QuotationDocumentReference>
    <cbc:ID>55</cbc:ID>
  </cac:QuotationDocumentReference>
  <cac:OrderDocumentReference>
    <cbc:ID>4</cbc:ID>
  </cac:OrderDocumentReference>
  <cac:OriginatorDocumentReference>
    <cbc:ID>REQ-1</cbc:ID>
  </cac:OriginatorDocumentReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>100</cbc:ID>
    <cbc:DocumentType>Blueprint</cbc:DocumentType>
    <cac:Attachment>
          <cac:ExternalReference>
        <cbc:URI>http://upload.wikimedia.org/wikipedia/commons/1/10/LaBelle_Blueprint.jpg</cbc:URI>
      </cac:ExternalReference>
    </cac:Attachment>
  </cac:AdditionalDocumentReference>
  <cac:Contract>
    <cbc:ID>C1</cbc:ID>
  </cac:Contract>
  <cac:BuyerCustomerParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0007">**********</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0088">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>City Hospital</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
		<cbc:StreetName>Main street 4</cbc:StreetName>
        <cbc:AdditionalStreetName>Back door</cbc:AdditionalStreetName>
        <cbc:CityName>Eurocity</cbc:CityName>
        <cbc:PostalZone>11155</cbc:PostalZone>
        <cbc:CountrySubentity>Region A</cbc:CountrySubentity>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1:Alpha2">SE</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:CompanyID schemeID="SE:ORGNR">SE554127771101</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>City Hospital 345433</cbc:RegistrationName>
        <cbc:CompanyID schemeID="0088">*************</cbc:CompanyID>
        <cac:RegistrationAddress>
          <cbc:CityName>Eurocity</cbc:CityName>
          <cac:Country>
            <cbc:IdentificationCode listID="ISO3166-1:Alpha2">SE</cbc:IdentificationCode>
          </cac:Country>
        </cac:RegistrationAddress>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>Martin Foggerty</cbc:Name>
        <cbc:Telephone>+46555785488</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:BuyerCustomerParty>
  <cac:SellerSupplierParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0192">*********</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0192">*********</cbc:ID>
      </cac:PartyIdentification>
      <cac:PostalAddress>
        <cbc:StreetName>Harbour street</cbc:StreetName>
        <cbc:AdditionalStreetName>Dock 45</cbc:AdditionalStreetName>
        <cbc:CityName>Bergen</cbc:CityName>
        <cbc:PostalZone>5005</cbc:PostalZone>
        <cbc:CountrySubentity>Region West</cbc:CountrySubentity>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1:Alpha2">NO</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>Cod Liver Oil Limited</cbc:RegistrationName>
      </cac:PartyLegalEntity>
       <cac:Contact>
        <cbc:Name>Öystein</cbc:Name>
        <cbc:Telephone>+47555444333</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:SellerSupplierParty>
  <cac:OriginatorCustomerParty>
    <cac:Party>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0088">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>Surgery Department</cbc:Name>
      </cac:PartyName>
      <cac:Contact>
        <cbc:Name>Dr Bengt</cbc:Name>
        <cbc:Telephone>+***********</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:OriginatorCustomerParty>
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0007">**********</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0088">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>Swedish Hospitals</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>High Street 23</cbc:StreetName>
        <cbc:AdditionalStreetName>First floor</cbc:AdditionalStreetName>
        <cbc:CityName>Trondheim</cbc:CityName>
        <cbc:PostalZone>7005</cbc:PostalZone>
        <cbc:CountrySubentity>Region M</cbc:CountrySubentity>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1:Alpha2">NO</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>Swedish Hospitals AB</cbc:RegistrationName>
        <cbc:CompanyID>**********</cbc:CompanyID>
        <cac:RegistrationAddress>
          <cbc:CityName>Stockholm</cbc:CityName>
          <cac:Country>
            <cbc:IdentificationCode listID="ISO3166-1:Alpha2">SE</cbc:IdentificationCode>
          </cac:Country>
        </cac:RegistrationAddress>
      </cac:PartyLegalEntity>
    </cac:Party>
  </cac:AccountingCustomerParty>
  <cac:Delivery>
    <cac:DeliveryLocation>
      <cac:Address>
        <cbc:StreetName>Lower street 5</cbc:StreetName>
        <cbc:AdditionalStreetName>Docking gate 5</cbc:AdditionalStreetName>
        <cbc:CityName>Stockholm</cbc:CityName>
        <cbc:PostalZone>11120</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1:Alpha2">SE</cbc:IdentificationCode>
        </cac:Country>
      </cac:Address>
    </cac:DeliveryLocation>
    <cac:RequestedDeliveryPeriod>
      <cbc:StartDate>2013-07-15</cbc:StartDate>
      <cbc:EndDate>2013-07-16</cbc:EndDate>
    </cac:RequestedDeliveryPeriod>
    <cac:DeliveryParty>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0088">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>Hospital Stock management</cbc:Name>
      </cac:PartyName>
      <cac:Contact>
        <cbc:Name>John</cbc:Name>
        <cbc:Telephone>+************</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:DeliveryParty>
  </cac:Delivery>
  <cac:DeliveryTerms>
    <cbc:ID schemeID="INCOTERMS">DAP</cbc:ID>
    <cbc:SpecialTerms>These special terms applies to the delivery...</cbc:SpecialTerms>
    <cac:DeliveryLocation>
      <cbc:ID>schemeID="GLN">7054673128432</cbc:ID>
    </cac:DeliveryLocation>
  </cac:DeliveryTerms>
  <cac:AllowanceCharge>
    <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
    <cbc:AllowanceChargeReason>Freight cost</cbc:AllowanceChargeReason>
    <cbc:Amount currencyID="EUR">10</cbc:Amount>
  </cac:AllowanceCharge>
  <cac:AllowanceCharge>
    <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
    <cbc:AllowanceChargeReason>Agreed discount</cbc:AllowanceChargeReason>
    <cbc:Amount currencyID="EUR">10</cbc:Amount>
  </cac:AllowanceCharge>
    <cac:TaxTotal>
    <cbc:TaxAmount currencyID="EUR">5</cbc:TaxAmount>
  </cac:TaxTotal>
  <cac:AnticipatedMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="EUR">50</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="EUR">50</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="EUR">55</cbc:TaxInclusiveAmount>
    <cbc:AllowanceTotalAmount currencyID="EUR">10</cbc:AllowanceTotalAmount>
    <cbc:ChargeTotalAmount currencyID="EUR">10</cbc:ChargeTotalAmount>
    <cbc:PayableRoundingAmount currencyID="EUR">0</cbc:PayableRoundingAmount>
    <cbc:PayableAmount currencyID="EUR">55</cbc:PayableAmount>
  </cac:AnticipatedMonetaryTotal>
  <cac:OrderLine>
    <cbc:Note>This free text note can be used....</cbc:Note>
    <cac:LineItem>
      <cbc:ID>1</cbc:ID>
      <cbc:Quantity unitCode="NAR" unitCodeListID="UNECERec20">50</cbc:Quantity>
      <cbc:LineExtensionAmount currencyID="EUR">50</cbc:LineExtensionAmount>
      <cbc:PartialDeliveryIndicator>true</cbc:PartialDeliveryIndicator>
      <cbc:AccountingCost>MAFO-1</cbc:AccountingCost>
      <cac:Delivery>
        <cac:RequestedDeliveryPeriod>
          <cbc:StartDate>2013-07-15</cbc:StartDate>
          <cbc:EndDate>2013-07-16</cbc:EndDate>
        </cac:RequestedDeliveryPeriod>
      </cac:Delivery>
      <cac:OriginatorParty>
       <cac:PartyIdentification>
         <cbc:ID schemeID="0088">*************</cbc:ID>
        </cac:PartyIdentification>
        <cac:PartyName>
          <cbc:Name>Martin</cbc:Name>
        </cac:PartyName>
      </cac:OriginatorParty>
      <cac:Price>
        <cbc:PriceAmount currencyID="EUR">1</cbc:PriceAmount>
        <cbc:BaseQuantity unitCode="NAR" unitCodeListID="UNECERec20">1</cbc:BaseQuantity>
        <cac:AllowanceCharge>
          <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
          <cbc:Amount currencyID="EUR">1</cbc:Amount>
          <cbc:BaseAmount currencyID="EUR">2</cbc:BaseAmount>
        </cac:AllowanceCharge>
      </cac:Price>
      <cac:Item>
        <cbc:Description>Aluminium snow shovel with left-handed grip</cbc:Description>
        <cbc:Name>Snow shovel</cbc:Name>
        <cac:SellersItemIdentification>
          <cbc:ID>SN-33</cbc:ID>
        </cac:SellersItemIdentification>
        <cac:StandardItemIdentification>
          <cbc:ID schemeID="0160">09876543211234</cbc:ID>
        </cac:StandardItemIdentification>
        <cac:CommodityClassification>
          <cbc:ItemClassificationCode listID="MP">76455</cbc:ItemClassificationCode>
        </cac:CommodityClassification>
        <cac:ClassifiedTaxCategory>
          <cbc:ID  schemeID="UNCL5305">S</cbc:ID>
          <cbc:Percent>10</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
          </cac:TaxScheme>
        </cac:ClassifiedTaxCategory>
        <cac:AdditionalItemProperty>
          <cbc:Name>GRIP</cbc:Name>
          <cbc:Value>Left-handed</cbc:Value>
        </cac:AdditionalItemProperty>
        <cac:AdditionalItemProperty>
          <cbc:Name>Purpose</cbc:Name>
          <cbc:Value>Snow</cbc:Value>
        </cac:AdditionalItemProperty>
      </cac:Item>
    </cac:LineItem>
  </cac:OrderLine>
</Order>
