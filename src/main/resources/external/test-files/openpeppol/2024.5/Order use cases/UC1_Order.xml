<?xml version="1.0" encoding="UTF-8"?>
<Order xmlns:xs="http://www.w3.org/2001/XMLSchema"
    xmlns="urn:oasis:names:specification:ubl:schema:xsd:Order-2"
    xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
  <cbc:CustomizationID>urn:fdc:peppol.eu:poacc:trns:order:3</cbc:CustomizationID>
  <cbc:ProfileID>urn:fdc:peppol.eu:poacc:bis:order_only:3</cbc:ProfileID>
  <cbc:ID>1</cbc:ID>
  <cbc:IssueDate>2013-07-01</cbc:IssueDate>
  <cbc:IssueTime>05:10:10</cbc:IssueTime>
   <cbc:DocumentCurrencyCode listID="ISO4217">EUR</cbc:DocumentCurrencyCode>
  <cbc:AccountingCost>MAFO</cbc:AccountingCost>
  <cac:ValidityPeriod>
    <cbc:EndDate>2013-07-30</cbc:EndDate>
  </cac:ValidityPeriod>
  <cac:Contract>
    <cbc:ID>C1</cbc:ID>
  </cac:Contract>
  <cac:BuyerCustomerParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0088">*************</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0088">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>City Hospital</cbc:Name>
      </cac:PartyName>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>City Hospital 345433</cbc:RegistrationName>
        <cbc:CompanyID schemeID="0088">*************</cbc:CompanyID>
        <cac:RegistrationAddress>
          <cbc:CityName>Eurocity</cbc:CityName>
          <cac:Country>
            <cbc:IdentificationCode listID="ISO3166-1:Alpha2">SE</cbc:IdentificationCode>
          </cac:Country>
        </cac:RegistrationAddress>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>Martin Foggerty</cbc:Name>
        <cbc:Telephone>+46555785488</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:BuyerCustomerParty>
  <cac:SellerSupplierParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0192">*********</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0192">*********</cbc:ID>
      </cac:PartyIdentification>
      <cac:PostalAddress>
        <cbc:StreetName>Harbour street</cbc:StreetName>
        <cbc:AdditionalStreetName>Dock 45</cbc:AdditionalStreetName>
        <cbc:CityName>Bergen</cbc:CityName>
        <cbc:PostalZone>5005</cbc:PostalZone>
        <cbc:CountrySubentity>Region West</cbc:CountrySubentity>
        <cac:AddressLine>
          <cbc:Line>Gate 34</cbc:Line>
        </cac:AddressLine>
        <cac:Country>
          <cbc:IdentificationCode>NO</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>The Supplier AB</cbc:RegistrationName>
      </cac:PartyLegalEntity>
    </cac:Party>
  </cac:SellerSupplierParty>
  <cac:Delivery>
    <cac:DeliveryLocation>
      <cac:Address>
        <cbc:StreetName>Lower street 5</cbc:StreetName>
        <cbc:AdditionalStreetName>Reception</cbc:AdditionalStreetName>
        <cbc:CityName>Stockholm</cbc:CityName>
        <cbc:PostalZone>11120</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1:Alpha2">SE</cbc:IdentificationCode>
        </cac:Country>
      </cac:Address>
    </cac:DeliveryLocation>
    <cac:RequestedDeliveryPeriod>
      <cbc:StartDate>2013-07-15</cbc:StartDate>
      <cbc:EndDate>2013-07-16</cbc:EndDate>
    </cac:RequestedDeliveryPeriod>
    <cac:DeliveryParty>
      <cac:PartyName>
        <cbc:Name>Hospital Tourist Department</cbc:Name>
      </cac:PartyName>
      <cac:Contact>
        <cbc:Name>John</cbc:Name>
        <cbc:Telephone>+465558877523</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:DeliveryParty>
  </cac:Delivery>
  <cac:TaxTotal>
		<cbc:TaxAmount currencyID="EUR">28.75</cbc:TaxAmount>
  </cac:TaxTotal>
  <cac:AnticipatedMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="EUR">115</cbc:LineExtensionAmount>
    <cbc:PayableAmount currencyID="EUR">143.75</cbc:PayableAmount>
  </cac:AnticipatedMonetaryTotal>
  <cac:OrderLine>
    <cac:LineItem>
      <cbc:ID>1</cbc:ID>
      <cbc:Quantity unitCode="NAR" unitCodeListID="UNECERec20">10</cbc:Quantity>
      <cbc:LineExtensionAmount currencyID="EUR">40</cbc:LineExtensionAmount>
      <cbc:AccountingCost>MAFO-1</cbc:AccountingCost>
      <cac:Price>
        <cbc:PriceAmount currencyID="EUR">4</cbc:PriceAmount>
      </cac:Price>
       <cac:Item>
        <cbc:Description>1x12 pack sauce bags</cbc:Description>
        <cbc:Name>Brown sauce</cbc:Name>
        <cac:SellersItemIdentification>
          <cbc:ID>SN-33</cbc:ID>
        </cac:SellersItemIdentification>
        <cac:StandardItemIdentification>
          <cbc:ID schemeID="0160">**************</cbc:ID>
        </cac:StandardItemIdentification>
        <cac:ClassifiedTaxCategory>
          <cbc:ID schemeID="UNCL5305">S</cbc:ID>
          <cbc:Percent>25</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
          </cac:TaxScheme>
        </cac:ClassifiedTaxCategory>
        </cac:Item>
    </cac:LineItem>
  </cac:OrderLine>
  <cac:OrderLine>
    <cac:LineItem>
      <cbc:ID>2</cbc:ID>
      <cbc:Quantity unitCode="NAR" unitCodeListID="UNECERec20">5</cbc:Quantity>
      <cbc:LineExtensionAmount currencyID="EUR">30</cbc:LineExtensionAmount>
      <cbc:AccountingCost>MAFO-1</cbc:AccountingCost>
      <cac:Price>
        <cbc:PriceAmount currencyID="EUR">6</cbc:PriceAmount>
      </cac:Price>
       <cac:Item>
        <cbc:Description>1x12 pack sauce bags</cbc:Description>
        <cbc:Name>White sauce</cbc:Name>
        <cac:SellersItemIdentification>
          <cbc:ID>SN-34</cbc:ID>
        </cac:SellersItemIdentification>
        <cac:StandardItemIdentification>
          <cbc:ID schemeID="0160">**************</cbc:ID>
        </cac:StandardItemIdentification>
        <cac:ClassifiedTaxCategory>
          <cbc:ID schemeID="UNCL5305">S</cbc:ID>
          <cbc:Percent>25</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
          </cac:TaxScheme>
        </cac:ClassifiedTaxCategory>
        </cac:Item>
    </cac:LineItem>
  </cac:OrderLine>
  <cac:OrderLine>
    <cac:LineItem>
      <cbc:ID>3</cbc:ID>
      <cbc:Quantity unitCode="NAR" unitCodeListID="UNECERec20">15</cbc:Quantity>
      <cbc:LineExtensionAmount currencyID="EUR">45</cbc:LineExtensionAmount>
      <cbc:AccountingCost>MAFO-1</cbc:AccountingCost>
      <cac:Price>
        <cbc:PriceAmount currencyID="EUR">3</cbc:PriceAmount>
      </cac:Price>
       <cac:Item>
        <cbc:Description>1x12 pack sauce bags</cbc:Description>
        <cbc:Name>Pepper sauce</cbc:Name>
        <cac:SellersItemIdentification>
          <cbc:ID>SN-35</cbc:ID>
        </cac:SellersItemIdentification>
        <cac:StandardItemIdentification>
          <cbc:ID schemeID="0160">**************</cbc:ID>
        </cac:StandardItemIdentification>
        <cac:ClassifiedTaxCategory>
          <cbc:ID schemeID="UNCL5305">S</cbc:ID>
          <cbc:Percent>25</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
          </cac:TaxScheme>
        </cac:ClassifiedTaxCategory>
        </cac:Item>
    </cac:LineItem>
  </cac:OrderLine>
</Order>
