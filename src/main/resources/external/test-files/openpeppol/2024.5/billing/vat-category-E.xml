<?xml version="1.0" encoding="UTF-8"?>
<!-- PEPPOL BIS Billing, testfile showing the use of VAT category Z (Zero rated goods) -->
<Invoice  xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
 xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
 xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2">
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0</cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>Vat-Z</cbc:ID>
    <cbc:IssueDate>2018-08-30</cbc:IssueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
    <cbc:DocumentCurrencyCode>GBP</cbc:DocumentCurrencyCode>
    <cbc:BuyerReference>test reference</cbc:BuyerReference>
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0088">*************</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID>*************</cbc:ID>
            </cac:PartyIdentification>
            <cac:PostalAddress>
                <cbc:StreetName>Main street 2, Building 4</cbc:StreetName>
                <cbc:CityName>Big city</cbc:CityName>
                <cbc:PostalZone>54321</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>GB</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>GB928741974</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>The Sellercompany Incorporated</cbc:RegistrationName>
            </cac:PartyLegalEntity>
        </cac:Party>
    </cac:AccountingSupplierParty>
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0184">DK12345678</cbc:EndpointID>
            <cac:PostalAddress>
                <cbc:StreetName>Anystreet 8</cbc:StreetName>
                <cbc:AdditionalStreetName>Back door</cbc:AdditionalStreetName>
                <cbc:CityName>Anytown</cbc:CityName>
                <cbc:PostalZone>101</cbc:PostalZone>
                <cbc:CountrySubentity>RegionB</cbc:CountrySubentity>
                <cac:Country>
                    <cbc:IdentificationCode>DK</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>The Buyercompany</cbc:RegistrationName>
            </cac:PartyLegalEntity>
        </cac:Party>
   </cac:AccountingCustomerParty>
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode>30</cbc:PaymentMeansCode>
        <cac:PayeeFinancialAccount>
            <cbc:ID>******************</cbc:ID>
            <cac:FinancialInstitutionBranch>
                <cbc:ID>SEXDABCD</cbc:ID>
            </cac:FinancialInstitutionBranch>
        </cac:PayeeFinancialAccount>
    </cac:PaymentMeans>
    <cac:PaymentTerms>
        <cbc:Note>Payment within 30 days</cbc:Note>
    </cac:PaymentTerms>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="GBP">0.00</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="GBP">1200.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="GBP">0.00</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>E</cbc:ID>
                <cbc:Percent>0</cbc:Percent>
                <cbc:TaxExemptionReasonCode>VATEX-EU-F</cbc:TaxExemptionReasonCode>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>  
    </cac:TaxTotal>
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="GBP">1200.00</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="GBP">1200.00</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="GBP">1200.00</cbc:TaxInclusiveAmount>
        <cbc:PayableAmount currencyID="GBP">1200.00</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    <cac:InvoiceLine>
        <cbc:ID>1</cbc:ID>
        <cbc:InvoicedQuantity unitCode="EA">10</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="GBP">1200.00</cbc:LineExtensionAmount>
        <cac:OrderLineReference>
            <cbc:LineID>1</cbc:LineID>
        </cac:OrderLineReference>
        <cac:Item>
            <cbc:Name>Test item, category Z</cbc:Name>
         <cac:StandardItemIdentification>
             <cbc:ID schemeID="0160">192387129837129873</cbc:ID>
         </cac:StandardItemIdentification>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>E</cbc:ID>
                <cbc:Percent>0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="GBP">120.00</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>

</Invoice>
