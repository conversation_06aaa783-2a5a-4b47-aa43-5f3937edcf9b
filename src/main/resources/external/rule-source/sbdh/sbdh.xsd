<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
           xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
           elementFormDefault="qualified">

    <xs:element name="StandardBusinessDocument">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="StandardBusinessDocumentHeader">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="HeaderVersion" type="xs:string"/>
                            <xs:element name="Sender">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Identifier">
                                            <xs:complexType>
                                                <xs:simpleContent>
                                                    <xs:extension base="xs:string">
                                                        <xs:attribute name="Authority" type="xs:string" use="required"/>
                                                    </xs:extension>
                                                </xs:simpleContent>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="Receiver">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Identifier">
                                            <xs:complexType>
                                                <xs:simpleContent>
                                                    <xs:extension base="xs:string">
                                                        <xs:attribute name="Authority" type="xs:string" use="required"/>
                                                    </xs:extension>
                                                </xs:simpleContent>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="DocumentIdentification">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Standard" type="xs:string"/>
                                        <xs:element name="TypeVersion" type="xs:string"/>
                                        <xs:element name="InstanceIdentifier" type="xs:string"/>
                                        <xs:element name="Type" type="xs:string"/>
                                        <xs:element name="CreationDateAndTime" type="xs:dateTime"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="BusinessScope">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Scope" maxOccurs="unbounded">
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="Type" type="xs:string"/>
                                                    <xs:element name="InstanceIdentifier" type="xs:string"/>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
