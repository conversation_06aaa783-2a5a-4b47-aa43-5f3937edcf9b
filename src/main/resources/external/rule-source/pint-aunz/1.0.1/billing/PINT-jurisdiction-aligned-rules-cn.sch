<?xml version="1.0" encoding="UTF-8"?><schema xmlns="http://purl.oclc.org/dsdl/schematron" queryBinding="xslt2">
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:slack" as="xs:boolean">
    <xsl:param name="exp" as="xs:decimal"/>
    <xsl:param name="val" as="xs:decimal"/>
    <xsl:param name="slack" as="xs:decimal"/>
    <xsl:value-of select="xs:decimal($exp + $slack) &gt;= $val and xs:decimal($exp - $slack) &lt;= $val"/>
  </xsl:function>
  <ns prefix="ext" uri="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"/>
  <ns prefix="cbc" uri="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"/>
  <ns prefix="cac" uri="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"/>
  <ns prefix="qdt" uri="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDataTypes-2"/>
  <ns prefix="udt" uri="urn:oasis:names:specification:ubl:schema:xsd:UnqualifiedDataTypes-2"/>
  <ns prefix="cn" uri="urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2"/>
  <ns prefix="ubl" uri="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"/>
  <ns prefix="u" uri="utils"/>
  <ns prefix="xs" uri="http://www.w3.org/2001/XMLSchema"/>

	<let name="supplierCountry" value="       if  (/*/cac:AccountingSupplierParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode) then             upper-case(normalize-space(/*/cac:AccountingSupplierParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode))           else             'XX'"/>
	<let name="buyerCountry" value="   if (/*/cac:AccountingCustomerParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode) then   upper-case(normalize-space(/*/cac:AccountingCustomerParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode))   else   'XX'"/>

  <phase id="PINTmodelaligned_phase">
    <active pattern="UBL-modelaligned"/>
  </phase>
  <phase id="codelistaligned_phase">
    <active pattern="Codesmodelaligned"/>
  </phase>
  <pattern id="UBL-modelaligned">
    <rule context="cac:AdditionalDocumentReference">
      <assert id="aligned-ibrp-sr-43" flag="fatal" test="((cbc:DocumentTypeCode='130') or ((local-name(/*) = 'CreditNote') and (cbc:DocumentTypeCode='50')) or (not(cbc:ID/@schemeID) and not(cbc:DocumentTypeCode)))">[aligned-ibrp-sr-43]-Scheme identifier shall only be used for invoiced object (ibt-018) (document type code with value 130)</assert>
    </rule>
    <rule context="cac:Price/cac:AllowanceCharge">
      <assert id="aligned-ibrp-004" flag="fatal" test="not(cbc:BaseAmount) or xs:decimal(../cbc:PriceAmount) = xs:decimal(cbc:BaseAmount) - xs:decimal(cbc:Amount)">[aligned-ibrp-004]-Item net price (ibt-146) MUST equal (Gross price (ibt-148) - Price discount (ibt-147)) when gross price is provided.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator = false()]">
      <assert id="aligned-ibrp-054" flag="fatal" test="not(cbc:MultiplierFactorNumeric and cbc:BaseAmount) or u:slack(if (cbc:Amount) then cbc:Amount else 0, (xs:decimal(cbc:BaseAmount) * xs:decimal(cbc:MultiplierFactorNumeric)) div 100, 0.02)">[aligned-ibrp-054]-Allowance amount (ibt-092, ibt-136) must equal base amount (ibt-093, ibt-137) * percentage (ibt-194, ibt-138) /100 if base amount and percentage exists.</assert>
      <assert id="aligned-ibrp-032-aunz" flag="fatal" test="exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID) or not(parent::ubl:Invoice|parent::cn:CreditNote)">[aligned-ibrp-032-aunz]-Each Document level allowance (ibg-20) MUST have a Document level allowance tax category code (ibt-095).</assert>
      <assert id="aligned-ibrp-057" flag="fatal" test="not(cbc:MultiplierFactorNumeric or cbc:BaseAmount) or (cbc:MultiplierFactorNumeric and cbc:BaseAmount)">[aligned-ibrp-057]-Either both or neither Allowance base amount (ibt-093) and percentage (ibt-094) MUST be provided.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator = true()]">
      <assert id="aligned-ibrp-055" flag="fatal" test="not(cbc:MultiplierFactorNumeric and cbc:BaseAmount) or u:slack(if (cbc:Amount) then cbc:Amount else 0, (xs:decimal(cbc:BaseAmount) * xs:decimal(cbc:MultiplierFactorNumeric)) div 100, 0.02)">[aligned-ibrp-055]-Charge amount (ibt-099, ibt-141) must equal base amount (ibt-100, ibt-142) * percentage (ibt-101, ibt-143) /100 if base amount and percentage exists.</assert>
      <assert id="aligned-ibrp-037-aunz" flag="fatal" test="exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID) or not(parent::ubl:Invoice|parent::cn:CreditNote)">[aligned-ibrp-037-aunz]-Each Document level charge (ibg-21) MUST have a Document level charge tax category code (ibt-102).</assert>
      <assert id="aligned-ibrp-058" flag="fatal" test="not(cbc:MultiplierFactorNumeric or cbc:BaseAmount) or (cbc:MultiplierFactorNumeric and cbc:BaseAmount)">[aligned-ibrp-058]-Either both or neither Charge base amount (ibt-100) and percentage (ibt-101) MUST be provided.</assert>
    </rule>
    <rule context="cac:InvoiceLine | cac:CreditNoteLine">

      <assert id="aligned-ibrp-050-aunz" flag="fatal" test="(cac:Item/cac:ClassifiedTaxCategory[cac:TaxScheme/(normalize-space(upper-case(cbc:ID))='GST')]/cbc:ID)">[aligned-ibrp-050-aunz]-Each Invoice line (ibg-25) MUST be categorized with an Invoiced item tax category code (ibt-151).</assert>
      <assert id="aligned-ibrp-053" flag="fatal" test="(exists(cac:Price/cbc:BaseQuantity) and u:slack(xs:decimal(cbc:LineExtensionAmount), xs:decimal(((cbc:InvoicedQuantity|cbc:CreditedQuantity) * (cac:Price/cbc:PriceAmount div cac:Price/cbc:BaseQuantity)) + sum(cac:AllowanceCharge[cbc:ChargeIndicator = true()]/cbc:Amount) - sum(cac:AllowanceCharge[cbc:ChargeIndicator = false()]/cbc:Amount)), 0.02)) or (not(exists(cac:Price/cbc:BaseQuantity)) and u:slack(xs:decimal(cbc:LineExtensionAmount), xs:decimal(((cbc:InvoicedQuantity|cbc:CreditedQuantity) * cac:Price/cbc:PriceAmount) + sum(cac:AllowanceCharge[cbc:ChargeIndicator = true()]/cbc:Amount) - sum(cac:AllowanceCharge[cbc:ChargeIndicator = false()]/cbc:Amount)), 0.02))">[aligned-ibrp-053]-Invoice line net amount (ibt-131) MUST equal (Invoiced quantity (ibt-129) * (Item net price (ibt-146)/item price base quantity (ibt-149)) + Sum of invoice line charge amount (ibt-141) - sum of invoice line allowance amount (ibt-136).</assert>
      <assert id="aligned-ibrp-059" flag="fatal" test="(count(cac:Item/cac:ClassifiedTaxCategory) = 1)">[aligned-ibrp-059]-Invoice lines (ibg-25) MUST have one and only one classified tax category (ibt-151).</assert>
      <assert id="aligned-ibrp-060" flag="fatal" test="not(cac:DocumentReference) or (cac:DocumentReference/cbc:DocumentTypeCode='130')">[aligned-ibrp-060]-Line Document reference can only be used for Invoice line object (ibg-36)</assert>
    </rule>
    <rule context="/ubl:Invoice | /cn:CreditNote">
      <assert id="aligned-ibrp-001-aunz" flag="fatal" test="starts-with(normalize-space(cbc:CustomizationID/text()), 'urn:peppol:pint:billing-1@aunz-1')">[aligned-ibrp-001-aunz]-Specification identifier (ibt-024) MUST start with the value 'urn:peppol:pint:billing-1@aunz-1'.</assert>
      <assert id="aligned-ibrp-002" flag="fatal" test="/*/cbc:ProfileID and matches(normalize-space(/*/cbc:ProfileID), 'urn:peppol:bis:billing')">[aligned-ibrp-002]-Business process (ibt-023) MUST be in the format 'urn:peppol:bis:billing'.</assert>
      <assert id="aligned-ibrp-003" flag="fatal" test="cbc:BuyerReference or cac:OrderReference/cbc:ID">[aligned-ibrp-003]-A buyer reference (ibt-010) or purchase order reference (ibt-013) MUST be provided.</assert>
      <assert id="aligned-ibrp-006" flag="fatal" test="(exists(cbc:TaxPointDate) and not(cac:InvoicePeriod/cbc:DescriptionCode)) or (not(cbc:TaxPointDate) and exists(cac:InvoicePeriod/cbc:DescriptionCode)) or (not(cbc:TaxPointDate) and not(cac:InvoicePeriod/cbc:DescriptionCode))">[aligned-ibrp-006]-Tax point date (ibt-007) and tax point date code (ibt-008) are mutually exclusive.</assert>


      <assert id="aligned-ibrp-008" flag="fatal" test="(count(cac:PaymentTerms) &lt;= 1)">[aligned-ibrp-008]-Invoice terms (ibg-033) MUST occur maximum once.</assert>

      <assert id="aligned-ibrp-021" flag="fatal" test="count(cac:TaxTotal[cac:TaxSubtotal]) = 1">[aligned-ibrp-021]-Only one tax total (ibg-38) with tax subtotals (ibg-23) MUST be provided.</assert>
      <assert id="aligned-ibrp-022" flag="fatal" test="count(cac:TaxTotal[not(cac:TaxSubtotal)]) = (if (cbc:TaxCurrencyCode) then 1 else 0)">[aligned-ibrp-022]-Only one tax total without tax subtotals (ibg-37)  MUST be provided when tax currency code (ibt-006) is provided.</assert>

      <assert id="aligned-ibrp-e-01-aunz" flag="fatal" test="((exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'E']) or exists(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'E'])) and (count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'E']) = 1)) or (not(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'E']) and not(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'E']))">[aligned-ibrp-e-01-aunz]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the tax category code (ibt-151, ibt-095 or ibt-102) is "Exempt from tax" MUST contain exactly one tax breakdown (ibg-23) with the tax category code (ibt-118) equal to "Exempt from tax".</assert>

      <assert id="aligned-ibrp-g-01-aunz" flag="fatal" test="((exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'G']) or exists(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'G'])) and (count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'G']) = 1)) or (not(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'G']) and not(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'G']))">[aligned-ibrp-g-01-aunz]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the tax category code (ibt-151, ibt-095 or ibt-102) is "Export" MUST contain in the tax breakdown (ibg-23) exactly one tax category code (ibt-118) equal with "Export".</assert>

      <assert id="aligned-ibrp-o-01-aunz" flag="fatal" test="((exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O']) or exists(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O'])) and (count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O']) = 1)) or (not(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O']) and not(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O']))">[aligned-ibrp-o-01-aunz]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the tax category code (ibt-151, ibt-095 or ibt-102) is "Not subject to tax" MUST contain exactly one tax breakdown group (ibg-23) with the tax category code (ibt-118) equal to "Not subject to tax".</assert>

      <assert id="aligned-ibrp-o-11-aunz" flag="fatal" test="(exists(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O']) and count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) != 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']) = 0) or not(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O'])">[aligned-ibrp-o-11-aunz]-An Invoice that contains a tax breakdown group (ibg-23) with a tax category code (ibt-118) "Not subject to tax" MUST not contain other tax breakdown groups (ibg-23).</assert>
      <assert id="aligned-ibrp-o-12-aunz" flag="fatal" test="(exists(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O']) and count(//cac:ClassifiedTaxCategory[normalize-space(cbc:ID) != 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']) = 0) or not(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O'])">[aligned-ibrp-o-12-aunz]-An Invoice that contains a tax breakdown group (ibg-23) with a tax category code (ibt-118) "Not subject to tax" MUST not contain an Invoice line (ibg-25) where the Invoiced item tax category code (ibt-151) is not "Not subject to tax".</assert>
      <assert id="aligned-ibrp-o-13-aunz" flag="fatal" test="(exists(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O']) and count(//cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID) != 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']) = 0) or not(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O'])">[aligned-ibrp-o-13-aunz]-An Invoice that contains a tax breakdown group (ibg-23) with a tax category code (ibt-118) "Not subject to tax" MUST not contain Document level allowances (ibg-20) where Document level allowance tax category code (ibt-095) is not "Not subject to tax".</assert>
      <assert id="aligned-ibrp-o-14-aunz" flag="fatal" test="(exists(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O']) and count(//cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID) != 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']) = 0) or not(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'O'])">[aligned-ibrp-o-14-aunz]-An Invoice that contains a tax breakdown group (ibg-23) with a tax category code (ibt-118) "Not subject to tax" MUST not contain Document level charges (ibg-21) where Document level charge tax category code (ibt-102) is not "Not subject to tax".</assert>
      <assert id="aligned-ibrp-s-01-aunz" flag="fatal" test="((count(//cac:AllowanceCharge/cac:TaxCategory[normalize-space(cbc:ID) = 'S']) + count(//cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'S'])) &gt; 0 and count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'S']) &gt; 0) or ((count(//cac:AllowanceCharge/cac:TaxCategory[normalize-space(cbc:ID) = 'S']) + count(//cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'S'])) = 0 and count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'S']) = 0)">[aligned-ibrp-s-01-aunz]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the tax category code (ibt-151, ibt-095 or ibt-102) is "Standard rated" MUST contain in the tax breakdown (ibg-23) at least one tax category code (ibt-118) equal with "Standard rated".</assert>

      <assert id="aligned-ibrp-z-01-aunz" flag="fatal" test="((exists(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'Z']) or exists(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'Z'])) and (count(cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'Z']) = 1)) or (not(//cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'Z']) and not(//cac:ClassifiedTaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID[normalize-space(.) = 'Z']))">[aligned-ibrp-z-01-aunz]-An Invoice that contains an Invoice line (ibg-25), a Document level allowance (ibg-20) or a Document level charge (ibg-21) where the tax category code (ibt-151, ibt-095 or ibt-102) is "Zero rated" MUST contain in the tax breakdown (ibg-23) exactly one tax category code (ibt-118) equal with "Zero rated".</assert>

      <assert id="aligned-ibrp-sr-12-aunz" flag="fatal" test="(count(cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme[cac:TaxScheme/upper-case(cbc:ID)='GST']/cbc:CompanyID) &lt;= 1)">[aligned-ibrp-sr-12-aunz]-Seller tax identifier (ibt-031) MUST occur maximum once</assert>
      <assert id="aligned-ibrp-sr-13-aunz" flag="fatal" test="(count(cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme[cac:TaxScheme/upper-case(cbc:ID)!='GST']/cbc:CompanyID) &lt;= 1)">[aligned-ibrp-sr-13-aunz]-Seller tax registration (ibt-032) MUST occur maximum once</assert>
    </rule>
    <rule context="//*[ends-with(name(), 'Amount') and not(ends-with(name(),'PriceAmount')) and not(ancestor::cac:Price/cac:AllowanceCharge)]">
      <assert id="aligned-ibrp-005" flag="fatal" test="string-length(substring-after(.,'.'))&lt;=2">[aligned-ibrp-005]- Amounts MUST be decimal up to two fraction digits.</assert>
    </rule>
    <rule context="cac:TaxSubtotal">

      <assert id="aligned-ibrp-045" flag="fatal" test="exists(cbc:TaxableAmount)">[aligned-ibrp-045]-Each tax breakdown (ibg-23) MUST have a tax category taxable amount (ibt-116).</assert>
      <assert id="aligned-ibrp-046" flag="fatal" test="exists(cbc:TaxAmount)">[aligned-ibrp-046]-Each tax breakdown (ibg-23) MUST have a tax category tax amount (ibt-117).</assert>
      <assert id="aligned-ibrp-047-aunz" flag="fatal" test="exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:ID)">[aligned-ibrp-047-aunz]-Each tax breakdown (ibg-23) MUST be defined through a tax category code (ibt-118).</assert>
      <assert id="aligned-ibrp-048-aunz" flag="fatal" test="exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/cbc:Percent) or (cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/normalize-space(cbc:ID)='O')">[aligned-ibrp-048-aunz]-Each tax breakdown (ibg-23) MUST have a tax category rate (ibt-119), except if the Invoice is not subject to tax.</assert>

      <assert id="aligned-ibrp-051-aunz" flag="fatal" test="(round(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/xs:decimal(cbc:Percent)) = 0 and (round(xs:decimal(cbc:TaxAmount)) = 0)) or (round(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/xs:decimal(cbc:Percent)) != 0 and (u:slack(abs(xs:decimal(cbc:TaxAmount)) , round(abs(xs:decimal(cbc:TaxableAmount)) * (cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/xs:decimal(cbc:Percent) div 100) * 10 * 10) div 100, 1))) or (not(exists(cac:TaxCategory[cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']/xs:decimal(cbc:Percent))) and (round(xs:decimal(cbc:TaxAmount)) = 0))">[aligned-ibrp-051-aunz]-Tax category tax amount (ibt-117) = tax category taxable amount (ibt-116) x (tax category rate (ibt-119) / 100), rounded to two decimals.</assert>

    </rule>
    <rule context="cac:PaymentMeans[cbc:PaymentMeansCode='30' or cbc:PaymentMeansCode='58']/cac:PayeeFinancialAccount">
      <assert id="aligned-ibrp-016" flag="fatal" test="normalize-space(cbc:ID) != ''">[aligned-ibrp-016]-A Payment account identifier (ibt-084) MUST be present if Credit transfer (ibg-17) information is provided in the invoice.</assert>
    </rule>

    <rule context="/ubl:Invoice/cac:LegalMonetaryTotal">
      <assert id="aligned-ibrp-061" flag="fatal" test="((cbc:PayableAmount &gt; 0) and (exists(//cbc:DueDate) or exists(//cac:PaymentTerms/cbc:Note))) or (cbc:PayableAmount &lt;= 0)">[aligned-ibrp-061]-In case the Amount due for payment (ibt-115) is positive, either the Payment due date (ibt-009) or the Payment terms (ibt-020) shall be present.</assert>
    </rule>


    <rule context="cac:PaymentMeans">
      <assert id="aligned-ibrp-018" flag="fatal" test="(exists(cac:PayeeFinancialAccount/cbc:ID) and ((normalize-space(cbc:PaymentMeansCode) = '30') or (normalize-space(cbc:PaymentMeansCode) = '58') )) or ((normalize-space(cbc:PaymentMeansCode) != '30') and (normalize-space(cbc:PaymentMeansCode) != '58'))">[aligned-ibrp-018]-If the Payment means type code (ibt-081) means SEPA credit transfer, Local credit transfer or Non-SEPA international credit transfer, the Payment account identifier (ibt-084) MUST be present.</assert>
      <assert id="aligned-ibrp-065" flag="fatal" test="(count(cbc:PaymentID) &lt;= 1)">[aligned-ibrp-065]-Payment reference (ibt-083) MUST occur maximum once.</assert>
    </rule>

    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-s-05-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) &gt; 0)">[aligned-ibrp-s-05-aunz]-In an Invoice line (ibg-25) where the Invoiced item tax category code (ibt-151) is "Standard rated" the Invoiced item tax rate (ibt-152) MUST be greater than zero.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-s-06-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) &gt; 0)">[aligned-ibrp-s-06-aunz]-In a Document level allowance (ibg-20) where the Document level allowance tax category code (ibt-095) is "Standard rated" the Document level allowance tax rate (ibt-096) MUST be greater than zero.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-s-07-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) &gt; 0)">[aligned-ibrp-s-07-aunz]-In a Document level charge (ibg-21) where the Document level charge tax category code (ibt-102) is "Standard rated" the Document level charge tax rate (ibt-103) MUST be greater than zero.</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'S'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">

      <assert id="aligned-ibrp-s-08-aunz" flag="fatal" test="every $rate in xs:decimal(cbc:Percent) satisfies (((exists(//cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID) = 'S'][cac:Item/cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]) or exists(//cac:AllowanceCharge[cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate])) and (u:slack(../xs:decimal(cbc:TaxableAmount), sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='S'][cac:Item/ cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate]/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate]/xs:decimal(cbc:Amount)),1))) or ((exists(//cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID) = 'S'][cac:Item/cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]) or exists(//cac:AllowanceCharge[cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate])) and (u:slack(../xs:decimal(cbc:TaxableAmount), sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='S'][cac:Item/cac:ClassifiedTaxCategory/xs:decimal(cbc:Percent) =$rate]/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate]/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='S'][cac:TaxCategory/xs:decimal(cbc:Percent) = $rate]/xs:decimal(cbc:Amount)),1))))">[aligned-ibrp-s-08-aunz]-For each different value of tax category rate (ibt-119) where the tax category code (ibt-118) is "Standard rated", the tax category taxable amount (ibt-116) in a tax breakdown (ibg-23) MUST equal the sum of Invoice line net amounts (ibt-131) plus the sum of document level charge amounts (ibt-099) minus the sum of document level allowance amounts (ibt-092) where the tax category code (ibt-151, ibt-102, ibt-095) is "Standard rated" and the tax rate (ibt-152, ibt-103, ibt-096) equals the tax category rate (ibt-119).</assert>
      <assert id="aligned-ibrp-s-09-aunz" flag="fatal" test="u:slack(abs(xs:decimal(../cbc:TaxAmount)) , round((abs(xs:decimal(../cbc:TaxableAmount)) * (xs:decimal(cbc:Percent) div 100)) * 10 * 10) div 100 ,1 )">[aligned-ibrp-s-09-aunz]-The tax category tax amount (ibt-117) in a tax breakdown (ibg-23) where tax category code (ibt-118) is "Standard rated" MUST equal the tax category taxable amount (ibt-116) multiplied by the tax category rate (ibt-119).</assert>
      <assert id="aligned-ibrp-s-10-aunz" flag="fatal" test="not(cbc:TaxExemptionReason) and not(cbc:TaxExemptionReasonCode)">[aligned-ibrp-s-10-aunz]-A tax breakdown (ibg-23) with tax Category code (ibt-118) "Standard rate" MUST not have a tax exemption reason code (ibt-121) or tax exemption reason text (ibt-120).</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-z-05-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-z-05-aunz]-In an Invoice line (ibg-25) where the Invoiced item tax category code (ibt-151) is "Zero rated" the Invoiced item tax rate (ibt-152) MUST be 0 (zero).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-z-06-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-z-06-aunz]-In a Document level allowance (ibg-20) where the Document level allowance tax category code (ibt-095) is "Zero rated" the Document level allowance tax rate (ibt-096) MUST be 0 (zero).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-z-07-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-z-07-aunz]-In a Document level charge (ibg-21) where the Document level charge tax category code (ibt-102) is "Zero rated" the Document level charge tax rate (ibt-103) MUST be 0 (zero).</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'Z'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-z-08-aunz" flag="fatal" test="(exists(//cac:InvoiceLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:Amount))))) or (exists(//cac:CreditNoteLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='Z']/xs:decimal(cbc:Amount)))))">[aligned-ibrp-z-08-aunz]-In a tax breakdown (ibg-23) where tax category code (ibt-118) is "Zero rated" the tax category taxable amount (ibt-116) MUST equal the sum of Invoice line net amount (ibt-131) minus the sum of Document level allowance amounts (ibt-092) plus the sum of Document level charge amounts (ibt-099) where the tax category codes (ibt-151, ibt-095, ibt-102) are "Zero rated".</assert>
      <assert id="aligned-ibrp-z-09-aunz" flag="fatal" test="xs:decimal(../cbc:TaxAmount) = 0">[aligned-ibrp-z-09-aunz]-The tax category tax amount (ibt-117) in a tax breakdown (ibg-23) where tax category code (ibt-118) is "Zero rated" MUST equal 0 (zero).</assert>
      <assert id="aligned-ibrp-z-10-aunz" flag="fatal" test="not(cbc:TaxExemptionReason) and not(cbc:TaxExemptionReasonCode)">[aligned-ibrp-z-10-aunz]-A tax breakdown (ibg-23) with tax Category code (ibt-118) "Zero rated" MUST not have a tax exemption reason code (ibt-121) or tax exemption reason text (ibt-120).</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-e-05-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-e-05-aunz]-In an Invoice line (ibg-25) where the Invoiced item tax category code (ibt-151) is "Exempt from tax", the Invoiced item tax rate (ibt-152) MUST be 0 (zero). </assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-e-06-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-e-06-aunz]-In a Document level allowance (ibg-20) where the Document level allowance tax category code (ibt-095) is "Exempt from tax", the Document level allowance tax rate (ibt-096) MUST be 0 (zero).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-e-07-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-e-07-aunz]-In a Document level charge (ibg-21) where the Document level charge tax category code (ibt-102) is "Exempt from tax", the Document level charge tax rate (ibt-103) MUST be 0 (zero).</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'E'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-e-08-aunz" flag="fatal" test="(exists(//cac:InvoiceLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:Amount))))) or (exists(//cac:CreditNoteLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='E']/xs:decimal(cbc:Amount)))))">[aligned-ibrp-e-08-aunz]-In a tax breakdown (ibg-23) where the tax category code (ibt-118) is "Exempt from tax" the tax category taxable amount (ibt-116) MUST equal the sum of Invoice line net amounts (ibt-131) minus the sum of Document level allowance amounts (ibt-092) plus the sum of Document level charge amounts (ibt-099) where the tax category codes (ibt-151, ibt-095, ibt-102) are "Exempt from tax".</assert>
      <assert id="aligned-ibrp-e-09-aunz" flag="fatal" test="xs:decimal(../cbc:TaxAmount) = 0">[aligned-ibrp-e-09-aunz]-The tax category tax amount (ibt-117) In a tax breakdown (ibg-23) where the tax category code (ibt-118) equals "Exempt from tax" MUST equal 0 (zero).</assert>

    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'G'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'G'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-g-05-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-g-05-aunz]-In an Invoice line (ibg-25) where the Invoiced item tax category code (ibt-151) is "Export" the Invoiced item tax rate (ibt-152) MUST be 0 (zero).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='G'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-g-06-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-g-06-aunz]-In a Document level allowance (ibg-20) where the Document level allowance tax category code (ibt-095) is "Export" the Document level allowance tax rate (ibt-096) MUST be 0 (zero).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='G'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-g-07-aunz" flag="fatal" test="(xs:decimal(cbc:Percent) = 0)">[aligned-ibrp-g-07-aunz]-In a Document level charge (ibg-21) where the Document level charge tax category code (ibt-102) is "Export" the Document level charge tax rate (ibt-103) MUST be 0 (zero).</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'G'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-g-08-aunz" flag="fatal" test="(exists(//cac:InvoiceLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='G']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='G']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='G']/xs:decimal(cbc:Amount))))) or (exists(//cac:CreditNoteLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='G']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='G']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='G']/xs:decimal(cbc:Amount)))))">[aligned-ibrp-g-08-aunz]-In a tax breakdown (ibg-23) where the tax category code (ibt-118) is "Export" the tax category taxable amount (ibt-116) MUST equal the sum of Invoice line net amounts (ibt-131) minus the sum of Document level allowance amounts (ibt-092) plus the sum of Document level charge amounts (ibt-099) where the tax category codes (ibt-151, ibt-095, ibt-102) are "Export".</assert>
      <assert id="aligned-ibrp-g-09-aunz" flag="fatal" test="xs:decimal(../cbc:TaxAmount) = 0">[aligned-ibrp-g-09-aunz]-The tax category tax amount (ibt-117) in a tax breakdown (ibg-23) where the tax category code (ibt-118) is "Export" MUST be 0 (zero).</assert>

    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST'] | cac:CreditNoteLine/cac:Item/cac:ClassifiedTaxCategory[normalize-space(cbc:ID) = 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-o-05-aunz" flag="fatal" test="not(cbc:Percent)">[aligned-ibrp-o-05-aunz]-An Invoice line (ibg-25) where the tax category code (ibt-151) is "Not subject to tax" MUST not contain an Invoiced item tax rate (ibt-152).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=false()]/cac:TaxCategory[normalize-space(cbc:ID)='O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-o-06-aunz" flag="fatal" test="not(cbc:Percent)">[aligned-ibrp-o-06-aunz]-A Document level allowance (ibg-20) where tax category code (ibt-095) is "Not subject to tax" MUST not contain a Document level allowance tax rate (ibt-096).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator=true()]/cac:TaxCategory[normalize-space(cbc:ID)='O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-o-07-aunz" flag="fatal" test="not(cbc:Percent)">[aligned-ibrp-o-07-aunz]-A Document level charge (ibg-21) where the tax category code (ibt-102) is "Not subject to tax" MUST not contain a Document level charge tax rate (ibt-103).</assert>
    </rule>
    <rule context="/*/cac:TaxTotal/cac:TaxSubtotal/cac:TaxCategory[normalize-space(cbc:ID) = 'O'][cac:TaxScheme/normalize-space(upper-case(cbc:ID))='GST']">
      <assert id="aligned-ibrp-o-08-aunz" flag="fatal" test="(exists(//cac:InvoiceLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:InvoiceLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:Amount))))) or (exists(//cac:CreditNoteLine) and (xs:decimal(../cbc:TaxableAmount) = (sum(../../../cac:CreditNoteLine[cac:Item/cac:ClassifiedTaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:LineExtensionAmount)) + sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=true()][cac:TaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:Amount)) - sum(../../../cac:AllowanceCharge[cbc:ChargeIndicator=false()][cac:TaxCategory/normalize-space(cbc:ID)='O']/xs:decimal(cbc:Amount)))))">[aligned-ibrp-o-08-aunz]-In a tax breakdown (ibg-23) where the tax category code (ibt-118) is " Not subject to tax" the tax category taxable amount (ibt-116) MUST equal the sum of Invoice line net amounts (ibt-131) minus the sum of Document level allowance amounts (ibt-092) plus the sum of Document level charge amounts (ibt-099) where the tax category codes (ibt-151, ibt-095, ibt-102) are "Not subject to tax".</assert>
      <assert id="aligned-ibrp-o-09-aunz" flag="fatal" test="xs:decimal(../cbc:TaxAmount) = 0">[aligned-ibrp-o-09-aunz]-The tax category tax amount (ibt-117) in a tax breakdown (ibg-23) where the tax category code (ibt-118) is "Not subject to tax" MUST be 0 (zero).</assert>

    </rule>

  </pattern>
  <pattern id="Codesmodelaligned">

    <rule flag="fatal" context="cac:TaxCategory/cbc:ID | cac:ClassifiedTaxCategory/cbc:ID">
      <assert id="aligned-ibrp-cl-01-aunz" flag="fatal" test="( ( not(contains(normalize-space(.),' ')) and contains( ' E S Z G O ',concat(' ',normalize-space(.),' ') ) ) )">[aligned-ibrp-cl-01-aunz]-Invoice tax categories (ibt-095) (ibt-102) (ibt-118) (ibt-192) (ibt-151) MUST be coded using UNCL5305 code list.</assert>
    </rule>
    <rule flag="fatal" context="cac:InvoicePeriod/cbc:DescriptionCode">
      <assert id="aligned-ibrp-cl-02" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' 3 35 432 ', concat(' ', normalize-space(.), ' '))))">[aligned-ibrp-cl-02]-Tax point date code (ibt-008) MUST be coded using a restriction of UNTDID 2005.</assert>
    </rule>

    <rule flag="fatal" context="cbc:EmbeddedDocumentBinaryObject[@mimeCode]">
      <assert id="aligned-ibrp-cl-04" flag="fatal" test="((@mimeCode = 'application/pdf' or @mimeCode = 'image/png' or @mimeCode = 'image/jpeg' or @mimeCode = 'text/csv' or @mimeCode = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' or @mimeCode = 'application/vnd.oasis.opendocument.spreadsheet'))">[aligned-ibrp-cl-04]-Mime code (ibt-125-1) MUST be according to subset of IANA code list.</assert>
    </rule>


    
  
	
		<rule context="cac:AccountingSupplierParty/cac:Party[$supplierCountry = 'AU']">
			<assert id="aligned-ibr-001-aunz" flag="fatal" test="(string-length(cac:PartyLegalEntity/cbc:CompanyID)&gt;=1 and cac:PartyLegalEntity/cbc:CompanyID/@schemeID = '0151')">[aligned-ibr-001-aunz]-An invoice must contain the Seller's ABN (ibt-030) if Seller country (ibt-040) is Australia</assert>
		</rule>
		<rule context="cac:AccountingCustomerParty/cac:Party[$buyerCountry = 'AU']">
			<assert id="aligned-ibr-004-aunz" flag="fatal" test="(string-length(cac:PartyLegalEntity/cbc:CompanyID)&gt;=1 and cac:PartyLegalEntity/cbc:CompanyID/@schemeID = '0151')">[aligned-ibr-004-aunz]-An invoice must contain the Buyer's ABN (ibt-047) if Buyer country (ibt-055) is Australia</assert>
		</rule>	
	
		<rule context="cac:AccountingSupplierParty/cac:Party[$supplierCountry = 'NZ']">
			<assert id="aligned-ibr-002-aunz" flag="fatal" test="(string-length(cac:PartyLegalEntity/cbc:CompanyID)&gt;=1 and cac:PartyLegalEntity/cbc:CompanyID/@schemeID = '0088')">[aligned-ibr-002-aunz]-An invoice must contain the Seller's NZBN (ibt-030) if Seller country (ibt-040) is New Zealand</assert>   
		</rule>
		<rule context="cac:AccountingCustomerParty/cac:Party[$buyerCountry = 'NZ']">
			<assert id="aligned-ibr-005-aunz" flag="fatal" test="(string-length(cac:PartyLegalEntity/cbc:CompanyID)&gt;=1 and cac:PartyLegalEntity/cbc:CompanyID/@schemeID = '0088')">[aligned-ibr-005-aunz]-An invoice must contain the Buyer's NZBN (ibt-047) if Buyer country (ibt-055) is New Zealand</assert>
		</rule>		
		


  </pattern>
</schema>