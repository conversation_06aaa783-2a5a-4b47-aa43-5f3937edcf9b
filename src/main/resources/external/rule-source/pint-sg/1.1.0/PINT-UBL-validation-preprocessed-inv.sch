<?xml version="1.0" encoding="UTF-8"?><schema xmlns="http://purl.oclc.org/dsdl/schematron" queryBinding="xslt2">
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:gln" as="xs:boolean">
    <xsl:param name="val"/>
    <xsl:variable name="length" select="string-length($val) - 1"/>
    <xsl:variable name="digits" select="reverse(for $i in string-to-codepoints(substring($val, 0, $length + 1)) return $i - 48)"/>
    <xsl:variable name="weightedSum" select="sum(for $i in (0 to $length - 1) return $digits[$i + 1] * (1 + ((($i + 1) mod 2) * 2)))"/>
    <xsl:value-of select="(10 - ($weightedSum mod 10)) mod 10 = number(substring($val, $length + 1, 1))"/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:mod11" as="xs:boolean">
    <xsl:param name="val"/>
    <xsl:variable name="length" select="string-length($val) - 1"/>
    <xsl:variable name="digits" select="reverse(for $i in string-to-codepoints(substring($val, 0, $length + 1)) return $i - 48)"/>
    <xsl:variable name="weightedSum" select="sum(for $i in (0 to $length - 1) return $digits[$i + 1] * (($i mod 6) + 2))"/>
    <xsl:value-of select="number($val) &gt; 0 and (11 - ($weightedSum mod 11)) mod 11 = number(substring($val, $length + 1, 1))"/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:slack" as="xs:boolean">
    <xsl:param name="exp" as="xs:decimal"/>
    <xsl:param name="val" as="xs:decimal"/>
    <xsl:param name="slack" as="xs:decimal"/>
    <xsl:value-of select="xs:decimal($exp + $slack) &gt;= $val and xs:decimal($exp - $slack) &lt;= $val"/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:mod97-0208" as="xs:boolean">
    <xsl:param name="val"/>
    <xsl:variable name="checkdigits" select="substring($val,9,2)"/>
    <xsl:variable name="calculated_digits" select="xs:string(97 - (xs:integer(substring($val,1,8)) mod 97))"/>
    <xsl:value-of select="number($checkdigits) = number($calculated_digits)"/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:checkCodiceIPA" as="xs:boolean">
    <xsl:param name="arg" as="xs:string?"/>
    <xsl:variable name="allowed-characters">ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789</xsl:variable>
    <xsl:sequence select="if ( (string-length(translate($arg, $allowed-characters, '')) = 0) and (string-length($arg) = 6) ) then true() else false()"/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:checkCF" as="xs:boolean">
    <xsl:param name="arg" as="xs:string?"/>
    <xsl:sequence select="       if ( (string-length($arg) = 16) or (string-length($arg) = 11) )          then        (       if ((string-length($arg) = 16))        then       (       if (u:checkCF16($arg))        then       (       true()       )       else       (       false()       )       )       else       (       if(($arg castable as xs:integer)) then true() else false()              )       )       else       (       false()       )       "/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:checkCF16" as="xs:boolean">
    <xsl:param name="arg" as="xs:string?"/>
    <xsl:variable name="allowed-characters">ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz</xsl:variable>
    <xsl:sequence select="       if (  (string-length(translate(substring($arg,1,6), $allowed-characters, '')) = 0) and         (substring($arg,7,2) castable as xs:integer) and        (string-length(translate(substring($arg,9,1), $allowed-characters, '')) = 0) and        (substring($arg,10,2) castable as xs:integer) and         (substring($arg,12,3) castable as xsd:string) and        (substring($arg,15,1) castable as xs:integer) and         (string-length(translate(substring($arg,16,1), $allowed-characters, '')) = 0)       )        then true()       else false()       "/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:checkPIVAseIT" as="xs:boolean">
    <xsl:param name="arg" as="xs:string"/>
    <xsl:variable name="paese" select="substring($arg,1,2)"/>
    <xsl:variable name="codice" select="substring($arg,3)"/>
    <xsl:sequence select="              if ( $paese = 'IT' or $paese = 'it' )       then       (       if ( ( string-length($codice) = 11 ) and ( if (u:checkPIVA($codice)!=0) then false() else true() ))       then        (       true()       )       else       (       false()       )       )       else       (       true()       )              "/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:checkPIVA" as="xs:integer">
    <xsl:param name="arg" as="xs:string?"/>
    <xsl:sequence select="       if (not($arg castable as xs:integer))        then 1       else ( u:addPIVA($arg,xs:integer(0)) mod 10 )"/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:addPIVA" as="xs:integer">
    <xsl:param name="arg" as="xs:string"/>
    <xsl:param name="pari" as="xs:integer"/>
    <xsl:variable name="tappo" select="if (not($arg castable as xs:integer)) then 0 else 1"/>
    <xsl:variable name="mapper" select="if ($tappo = 0) then 0 else        ( if ($pari = 1)        then ( xs:integer(substring('0246813579', ( xs:integer(substring($arg,1,1)) +1 ) ,1)) )        else ( xs:integer(substring($arg,1,1) ) )       )"/>
    <xsl:sequence select="if ($tappo = 0) then $mapper else ( xs:integer($mapper) + u:addPIVA(substring(xs:string($arg),2), (if($pari=0) then 1 else 0) ) )"/>
  </xsl:function>
  <xsl:function xmlns:xsl="http://www.w3.org/1999/XSL/Transform" name="u:abn" as="xs:boolean">
    <xsl:param name="val"/>
    <xsl:value-of select="(       ((string-to-codepoints(substring($val,1,1)) - 49) * 10) +       ((string-to-codepoints(substring($val,2,1)) - 48) * 1) +       ((string-to-codepoints(substring($val,3,1)) - 48) * 3) +       ((string-to-codepoints(substring($val,4,1)) - 48) * 5) +       ((string-to-codepoints(substring($val,5,1)) - 48) * 7) +       ((string-to-codepoints(substring($val,6,1)) - 48) * 9) +       ((string-to-codepoints(substring($val,7,1)) - 48) * 11) +       ((string-to-codepoints(substring($val,8,1)) - 48) * 13) +       ((string-to-codepoints(substring($val,9,1)) - 48) * 15) +       ((string-to-codepoints(substring($val,10,1)) - 48) * 17) +       ((string-to-codepoints(substring($val,11,1)) - 48) * 19)) mod 89 = 0       "/>
  </xsl:function>
  <ns prefix="ext" uri="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"/>
  <ns prefix="cbc" uri="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"/>
  <ns prefix="cac" uri="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"/>
  <ns prefix="qdt" uri="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDataTypes-2"/>
  <ns prefix="udt" uri="urn:oasis:names:specification:ubl:schema:xsd:UnqualifiedDataTypes-2"/>
  <ns prefix="cn" uri="urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2"/>
  <ns prefix="ubl" uri="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"/>
  <ns prefix="u" uri="utils"/>
  <ns prefix="xs" uri="http://www.w3.org/2001/XMLSchema"/>
  <phase id="PINTmodel_phase">
    <active pattern="UBL-model"/>
  </phase>
  <phase id="codelist_phase">
    <active pattern="Codesmodel"/>
  </phase>
  <pattern id="UBL-model">
    <rule context="cbc:EndpointID[@schemeID = '0088'] | cac:PartyIdentification/cbc:ID[@schemeID = '0088'] | cbc:CompanyID[@schemeID = '0088']">
      <assert id="ibr-068" flag="fatal" test="matches(normalize-space(), '^[0-9]+$') and u:gln(normalize-space())">[ibr-068]-GLN MUST have a valid format according to GS1 rules for endpoints (ibt-034), (ibt-049), party identification (ibt-029), (ibt-046), (ibt-060), legal entities (ibt-030), (ibt-047), (ibt-061).</assert>
    </rule>
    <rule context="cbc:EndpointID[@schemeID = '0007'] | cac:PartyIdentification/cbc:ID[@schemeID = '0007'] | cbc:CompanyID[@schemeID = '0007']">
      <assert id="ibr-127" flag="fatal" test="string-length(normalize-space()) = 10 and string(number(normalize-space())) != 'NaN'">[ibr-127]-Swedish organization number MUST be stated in the correct format for endpoints (ibt-034), (ibt-049), party identification (ibt-029), (ibt-046), (ibt-060), legal entities (ibt-030), (ibt-047), (ibt-061).</assert>
    </rule>
    <rule context="cbc:EndpointID[@schemeID = '0192'] | cac:PartyIdentification/cbc:ID[@schemeID = '0192'] | cbc:CompanyID[@schemeID = '0192']">
      <assert id="ibr-069" flag="fatal" test="matches(normalize-space(), '^[0-9]{9}$') and u:mod11(normalize-space())">[ibr-069]-Norwegian organization number MUST be stated in the correct format for endpoints (ibt-034), (ibt-049), party identification (ibt-029), (ibt-046), (ibt-060), legal entities (ibt-030), (ibt-047), (ibt-061).</assert>
    </rule>
    <rule context="cbc:EndpointID[@schemeID = '0184'] | cac:PartyIdentification/cbc:ID[@schemeID = '0184'] | cbc:CompanyID[@schemeID = '0184']">
      <assert id="ibr-070" flag="fatal" test="(string-length(text()) = 10) and (substring(text(), 1, 2) = 'DK') and (string-length(translate(substring(text(), 3, 8), '1234567890', '')) = 0)">[ibr-070]-Danish organization number (CVR) MUST be stated in the correct format for endpoints (ibt-034), (ibt-049), party identification (ibt-029), (ibt-046), (ibt-060), legal entities (ibt-030), (ibt-047), (ibt-061).</assert>
    </rule>
    <rule context="cbc:EndpointID[@schemeID = '0208'] | cac:PartyIdentification/cbc:ID[@schemeID = '0208'] | cbc:CompanyID[@schemeID = '0208']">
      <assert id="ibr-113" flag="fatal" test="matches(normalize-space(), '^[0-9]{10}$') and u:mod97-0208(normalize-space())">[ibr-113]-Belgian enterprise number MUST be stated in the correct format for endpoints (ibt-034), (ibt-049), party identification (ibt-029), (ibt-046), (ibt-060), legal entities (ibt-030), (ibt-047), (ibt-061).</assert>
    </rule>
    <rule context="cbc:EndpointID[@schemeID = '0201'] | cac:PartyIdentification/cbc:ID[@schemeID = '0201'] | cbc:CompanyID[@schemeID = '0201']">
      <assert id="ibr-114" flag="fatal" test="u:checkCodiceIPA(normalize-space())">[ibr-114]-IPA Code (Codice Univoco Unità Organizzativa) must be stated in the correct format for endpoints (ibt-034), (ibt-049), party identification (ibt-029), (ibt-046), (ibt-060), legal entities (ibt-030), (ibt-047), (ibt-061).</assert>
    </rule>
    <rule context="cbc:EndpointID[@schemeID = '0210'] | cac:PartyIdentification/cbc:ID[@schemeID = '0210'] | cbc:CompanyID[@schemeID = '0210'] | cbc:EndpointID[@schemeID = '9907'] | cac:PartyIdentification/cbc:ID[@schemeID = '9907'] | cbc:CompanyID[@schemeID = '9907']">
      <assert id="ibr-115" flag="fatal" test="u:checkCF(normalize-space())">[ibr-115]-Tax Code (Codice Fiscale) must be stated in the correct format for endpoints (ibt-034), (ibt-049), party identification (ibt-029), (ibt-046), (ibt-060), legal entities (ibt-030), (ibt-047), (ibt-061).</assert>
    </rule>
    <rule context="cbc:EndpointID[@schemeID = '0211'] | cac:PartyIdentification/cbc:ID[@schemeID = '0211'] | cbc:CompanyID[@schemeID = '0211'] | cbc:EndpointID[@schemeID = '9906'] | cac:PartyIdentification/cbc:ID[@schemeID = '9906'] | cbc:CompanyID[@schemeID = '9906']">
      <assert id="ibr-116" flag="fatal" test="u:checkPIVAseIT(normalize-space())">[ibr-116]-Italian VAT Code (Partita Iva) must be stated in the correct format for endpoints (ibt-034), (ibt-049), party identification (ibt-029), (ibt-046), (ibt-060), legal entities (ibt-030), (ibt-047), (ibt-061).</assert>
    </rule>
    <rule context="cbc:EndpointID[@schemeID = '0151'] | cac:PartyIdentification/cbc:ID[@schemeID = '0151'] | cbc:CompanyID[@schemeID = '0151']">
      <assert id="ibr-120" flag="fatal" test="matches(normalize-space(), '^[0-9]{11}$') and u:abn(normalize-space())">[ibr-120]-Australian Business Number (ABN) must be stated in the correct format for  for endpoints (ibt-034), (ibt-049), party identification (ibt-029), (ibt-046), (ibt-060), legal entities (ibt-030), (ibt-047), (ibt-061).</assert>
    </rule>
    <rule context="cac:Price/cac:AllowanceCharge">
      <assert id="ibr-083" flag="fatal" test="normalize-space(cbc:ChargeIndicator/text()) = 'false'">[ibr-083]-Charge on price level (ibt-147) is NOT allowed. Only value 'false' allowed.</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator = false()]">
      <assert id="ibr-sr-30" flag="fatal" test="(count(cbc:AllowanceChargeReason) &lt;= 1)">[ibr-sr-30]-Allowance reason (ibt-097) MUST occur maximum once</assert>
      <assert id="ibr-031" flag="fatal" test="((exists(cbc:Amount) and  not(ancestor::cac:InvoiceLine | ancestor::cac:CreditNoteLine))) or (ancestor::cac:InvoiceLine | ancestor::cac:CreditNoteLine)">[ibr-031]-Allowance (ibg-20) MUST have an allowance amount (ibt-092).</assert>
      <assert id="ibr-033" flag="fatal" test="((exists(cbc:AllowanceChargeReason) or exists(cbc:AllowanceChargeReasonCode)) and not(exists(ancestor::cac:InvoiceLine|ancestor::cac:CreditNoteLine))) or (ancestor::cac:InvoiceLine | ancestor::cac:CreditNoteLine)">[ibr-033]-Each Document level allowance (ibg-20) MUST have a Document level allowance reason (ibt-097) or a Document level allowance reason code (ibt-098).</assert>
      <assert id="ibr-041" flag="fatal" test="exists(cbc:Amount) or not(exists(ancestor::cac:InvoiceLine|ancestor::cac:CreditNoteLine))">[ibr-041]-Each Invoice line allowance (ibg-27) MUST have an Invoice line allowance amount (ibt-136).</assert>
      <assert id="ibr-042" flag="fatal" test="exists(cbc:AllowanceChargeReason) or exists(cbc:AllowanceChargeReasonCode) or not(exists(ancestor::cac:InvoiceLine|ancestor::cac:CreditNoteLine))">[ibr-042]-Each Invoice line allowance (ibg-27) MUST have an Invoice line allowance reason (ibt-139) or an Invoice line allowance reason code (ibt-140).</assert>
    </rule>
    <rule context="cac:AllowanceCharge[cbc:ChargeIndicator = true()]">
      <assert id="ibr-036" flag="fatal" test="((exists(cbc:Amount) and  not(ancestor::cac:InvoiceLine | ancestor::cac:CreditNoteLine))) or (ancestor::cac:InvoiceLine | ancestor::cac:CreditNoteLine)">[ibr-036]-Charge (ibg-21) MUST have a charge amount (ibt-099).</assert>
      <assert id="ibr-sr-31" flag="fatal" test="(count(cbc:AllowanceChargeReason) &lt;= 1)">[ibr-sr-31]-Charge reason (ibt-104) MUST occur maximum once</assert>
      <assert id="ibr-038" flag="fatal" test="((exists(cbc:AllowanceChargeReason) or exists(cbc:AllowanceChargeReasonCode)) and not(exists(ancestor::cac:InvoiceLine|ancestor::cac:CreditNoteLine))) or (ancestor::cac:InvoiceLine | ancestor::cac:CreditNoteLine)">[ibr-038]-Each Document level charge (ibg-21) MUST have a Document level charge reason (ibt-104) or a Document level charge reason code (ibt-105).</assert>
      <assert id="ibr-043" flag="fatal" test="exists(cbc:Amount) or not(exists(ancestor::cac:InvoiceLine|ancestor::cac:CreditNoteLine))">[ibr-043]-Each Invoice line charge (ibg-28) MUST have an Invoice line charge amount (ibt-141).</assert>
      <assert id="ibr-044" flag="fatal" test="exists(cbc:AllowanceChargeReason) or exists(cbc:AllowanceChargeReasonCode)">[ibr-044]-Each Invoice line charge (ibg-28) MUST have an Invoice line charge reason (ibt-144) or an invoice line charge reason code (ibt-145). </assert>
    </rule>
    <rule context="cac:AllowanceCharge">
      <assert id="ibr-082" flag="fatal" test="normalize-space(cbc:ChargeIndicator/text()) = 'true' or normalize-space(cbc:ChargeIndicator/text()) = 'false'">[ibr-082]-Allowance/charge indicator value MUST equal 'true' or 'false'.</assert>
    </rule>
    <rule context="cac:AllowanceCharge/cac:TaxCategory">
      <assert id="ibr-sr-61" flag="fatal" test="(count(cbc:TaxExemptionReason) &lt;= 1)">[ibr-sr-61]-Allowance/charge tax exemption reason (ibt-197) (ibt-199)  MUST occur maximum once.</assert>
    </rule>
    <rule context="cac:AdditionalDocumentReference">
      <assert id="ibr-052" flag="fatal" test="(normalize-space(cbc:ID)) != ''">[ibr-052]-Each Additional supporting document (ibg-24) MUST contain a Supporting document reference (ibt-122).    </assert>
      <assert id="ibr-sr-33" flag="fatal" test="(count(cbc:DocumentDescription) &lt;= 1)">[ibr-sr-33]-Supporting document description (ibt-123) MUST occur maximum once</assert>
    </rule>
    <rule context="cac:AccountingCustomerParty/cac:Party">
      <assert id="ibr-080" flag="fatal" test="cbc:EndpointID">[ibr-080]-The Buyer electronic address (ibt-049) MUST be provided.</assert>
    </rule>
    <rule context="cac:AccountingCustomerParty/cac:Party/cbc:EndpointID">
      <assert id="ibr-063" flag="fatal" test="exists(@schemeID)">[ibr-063]-The Buyer electronic address (ibt-049) MUST have a Scheme identifier.    </assert>
    </rule>
    <rule context="cac:AccountingCustomerParty/cac:Party/cac:PostalAddress">
      <assert id="ibr-011" flag="fatal" test="normalize-space(cac:Country/cbc:IdentificationCode) != ''">[ibr-011]-The Buyer postal address (ibg-08) MUST contain a Buyer country code (ibt-055).</assert>
      <assert id="ibr-sr-54" flag="fatal" test="(count(cac:AddressLine/cbc:Line) &lt;= 1)">[ibr-sr-54]-The Buyer Postal Address Line (ibt-163) MUST occur maximum once</assert>
    </rule>
    <rule context="cac:Delivery/cac:DeliveryLocation/cac:Address">
      <assert id="ibr-057" flag="fatal" test="exists(cac:Country/cbc:IdentificationCode)">[ibr-057]-Each Deliver to address (ibg-15) MUST contain a Deliver to country code (ibt-080).</assert>
      <assert id="ibr-sr-56" flag="fatal" test="(count(cac:AddressLine/cbc:Line) &lt;= 1)">[ibr-sr-56]-The Delivery Postal Address Line (ibt-165) MUST occur maximum once</assert>
    </rule>
    <rule context="cac:LegalMonetaryTotal">
      <assert id="ibr-012" flag="fatal" test="exists(cbc:LineExtensionAmount)">[ibr-012]-An Invoice MUST have the Sum of Invoice line net amount (ibt-106).</assert>
      <assert id="ibr-013" flag="fatal" test="exists(cbc:TaxExclusiveAmount)">[ibr-013]-An Invoice MUST have the Invoice total amount without Tax (ibt-109).</assert>
      <assert id="ibr-014" flag="fatal" test="exists(cbc:TaxInclusiveAmount)">[ibr-014]-An Invoice MUST have the Invoice total amount with Tax (ibt-112).</assert>
      <assert id="ibr-015" flag="fatal" test="exists(cbc:PayableAmount)">[ibr-015]-An Invoice MUST have the Amount due for payment (ibt-115).</assert>
      <assert id="ibr-091" flag="fatal" test="string-length(substring-after(cbc:PayableAmount, '.')) &lt;= 2">[ibr-091]-Invoice amount due for payment (ibt-115) MUST have no more than 2 decimals.</assert>
      <assert id="ibr-121" flag="fatal" test="string-length(substring-after(cbc:AllowanceTotalAmount, '.')) &lt;= 2">[ibr-121]-Document level allowance amount (ibt-107) MUST have no more than 2 decimals.</assert>
      <assert id="ibr-122" flag="fatal" test="string-length(substring-after(cbc:ChargeTotalAmount, '.')) &lt;= 2">[ibr-122]-Document level charge amount (ibt-108) MUST have no more than 2 decimals.</assert>
      <assert id="ibr-123" flag="fatal" test="string-length(substring-after(cbc:TaxExclusiveAmount, '.')) &lt;= 2">[ibr-123]-Invoice total amount without TAX (ibt-109) MUST have no more than 2 decimals.</assert>
      <assert id="ibr-125" flag="fatal" test="string-length(substring-after(cbc:TaxInclusiveAmount, '.')) &lt;= 2">[ibr-125]- Invoice total amount with TAX (ibt-112) MUST have no more than 2 decimals.</assert>
      <assert id="ibr-co-10" flag="fatal" test="(xs:decimal(cbc:LineExtensionAmount) = (round(sum(//(cac:InvoiceLine|cac:CreditNoteLine)/xs:decimal(cbc:LineExtensionAmount)) * 10 * 10) div 100))">[ibr-co-10]-Sum of Invoice line net amount (ibt-106) = Σ Invoice line net amount (ibt-131).</assert>
      <assert id="ibr-co-11" flag="fatal" test="xs:decimal(cbc:AllowanceTotalAmount) = (round(sum(../cac:AllowanceCharge[cbc:ChargeIndicator=false()]/xs:decimal(cbc:Amount)) * 10 * 10) div 100) or  (not(cbc:AllowanceTotalAmount) and not(../cac:AllowanceCharge[cbc:ChargeIndicator=false()]))">[ibr-co-11]-Sum of allowances on document level (ibt-107) = Σ Document level allowance amount (ibt-092).</assert>
      <assert id="ibr-co-12" flag="fatal" test="xs:decimal(cbc:ChargeTotalAmount) = (round(sum(../cac:AllowanceCharge[cbc:ChargeIndicator=true()]/xs:decimal(cbc:Amount)) * 10 * 10) div 100) or (not(cbc:ChargeTotalAmount) and not(../cac:AllowanceCharge[cbc:ChargeIndicator=true()]))">[ibr-co-12]-Sum of charges on document level (ibt-108) = Σ Document level charge amount (ibt-099).</assert>
      <assert id="ibr-co-13" flag="fatal" test="../cac:TaxTotal/cbc:TaxIncludedIndicator = true() or (((cbc:ChargeTotalAmount) and (cbc:AllowanceTotalAmount) and (xs:decimal(cbc:TaxExclusiveAmount) = round((xs:decimal(cbc:LineExtensionAmount) + xs:decimal(cbc:ChargeTotalAmount) - xs:decimal(cbc:AllowanceTotalAmount)) * 10 * 10) div 100 ))  or (not(cbc:ChargeTotalAmount) and (cbc:AllowanceTotalAmount) and (xs:decimal(cbc:TaxExclusiveAmount) = round((xs:decimal(cbc:LineExtensionAmount) - xs:decimal(cbc:AllowanceTotalAmount)) * 10 * 10 ) div 100)) or ((cbc:ChargeTotalAmount) and not(cbc:AllowanceTotalAmount) and (xs:decimal(cbc:TaxExclusiveAmount) = round((xs:decimal(cbc:LineExtensionAmount) + xs:decimal(cbc:ChargeTotalAmount)) * 10 * 10 ) div 100)) or (not(cbc:ChargeTotalAmount) and not(cbc:AllowanceTotalAmount) and (xs:decimal(cbc:TaxExclusiveAmount) = xs:decimal(cbc:LineExtensionAmount))))">[ibr-co-13]-Invoice total amount without Tax (ibt-109) = Σ Invoice line net amount (ibt-131) - Sum of allowances on document level (ibt-107) + Sum of charges on document level (ibt-108).</assert>
      <assert id="ibr-co-16" flag="fatal" test="(xs:decimal(cbc:PrepaidAmount) and not(xs:decimal(cbc:PayableRoundingAmount)) and (xs:decimal(cbc:PayableAmount) = (round((xs:decimal(cbc:TaxInclusiveAmount) - xs:decimal(cbc:PrepaidAmount)) * 10 * 10) div 100))) or (not(xs:decimal(cbc:PrepaidAmount)) and not(xs:decimal(cbc:PayableRoundingAmount)) and xs:decimal(cbc:PayableAmount) = xs:decimal(cbc:TaxInclusiveAmount)) or (xs:decimal(cbc:PrepaidAmount) and xs:decimal(cbc:PayableRoundingAmount) and ((round((xs:decimal(cbc:PayableAmount) - xs:decimal(cbc:PayableRoundingAmount)) * 10 * 10) div 100) = (round((xs:decimal(cbc:TaxInclusiveAmount) - xs:decimal(cbc:PrepaidAmount)) * 10 * 10) div 100))) or (not(xs:decimal(cbc:PrepaidAmount)) and xs:decimal(cbc:PayableRoundingAmount) and ((round((xs:decimal(cbc:PayableAmount) - xs:decimal(cbc:PayableRoundingAmount)) * 10 * 10) div 100) = xs:decimal(cbc:TaxInclusiveAmount)))">[ibr-co-16]-Amount due for payment (ibt-115) = Invoice total amount with Tax (ibt-112) - Paid amount (ibt-113) + Rounding amount (ibt-114).</assert>
    </rule>
    <rule context="/ubl:Invoice | /cn:CreditNote">
      <assert id="ibr-001" flag="fatal" test="normalize-space(cbc:CustomizationID) != ''">[ibr-001]-An Invoice MUST have a Specification identifier (ibt-024).</assert>
      <assert id="ibr-002" flag="fatal" test="normalize-space(cbc:ID) !=''">[ibr-002]-An Invoice MUST have an Invoice number (ibt-001).</assert>
      <assert id="ibr-003" flag="fatal" test="normalize-space(cbc:IssueDate) !=''">[ibr-003]-An Invoice MUST have an Invoice issue date (ibt-002).</assert>
      <assert id="ibr-004" flag="fatal" test="normalize-space(cbc:InvoiceTypeCode) !='' or normalize-space(cbc:CreditNoteTypeCode) !=''">[ibr-004]-An Invoice MUST have an Invoice type code (ibt-003).</assert>
      <assert id="ibr-005" flag="fatal" test="normalize-space(cbc:DocumentCurrencyCode) !=''">[ibr-005]-An Invoice MUST have an Invoice currency code (ibt-005).</assert>
      <assert id="ibr-006" flag="fatal" test="normalize-space(cac:AccountingSupplierParty/cac:Party/cac:PartyLegalEntity/cbc:RegistrationName) !=''">[ibr-006]-An Invoice MUST contain the Seller name (ibt-027).</assert>
      <assert id="ibr-007" flag="fatal" test="normalize-space(cac:AccountingCustomerParty/cac:Party/cac:PartyLegalEntity/cbc:RegistrationName) !=''">[ibr-007]-An Invoice MUST contain the Buyer name (ibt-044).</assert>
      <assert id="ibr-008" flag="fatal" test="exists(cac:AccountingSupplierParty/cac:Party/cac:PostalAddress)">[ibr-008]-An Invoice MUST contain the Seller postal address (ibg-05). </assert>
      <assert id="ibr-010" flag="fatal" test="exists(cac:AccountingCustomerParty/cac:Party/cac:PostalAddress)">[ibr-010]-An Invoice MUST contain the Buyer postal address (ibg-08).</assert>
      <assert id="ibr-016" flag="fatal" test="exists(cac:InvoiceLine) or exists(cac:CreditNoteLine)">[ibr-016]-An Invoice MUST have at least one Invoice line (ibg-25)</assert>
      <assert id="ibr-053" flag="fatal" test="every $taxcurrency in cbc:TaxCurrencyCode satisfies exists(//cac:TaxTotal/cbc:TaxAmount[@currencyID=$taxcurrency])">[ibr-053]-If the Tax accounting currency code (ibt-006) is present, then the Invoice total Tax amount in accounting currency (ibt-111) MUST be provided.</assert>
      <assert id="ibr-066" flag="fatal" test="count(cac:PaymentMeans/cac:CardAccount) &lt;= 1">[ibr-066]-An Invoice MUST contain maximum one Payment Card account (ibg-18).</assert>
      <assert id="ibr-067" flag="fatal" test="count(cac:PaymentMeans/cac:PaymentMandate) &lt;= 1">[ibr-067]-An Invoice MUST contain maximum one Payment Mandate (ibg-19).</assert>
      <assert id="ibr-071" flag="fatal" test="not(//cac:AdditionalDocumentReference[cbc:DocumentTypeCode = '130']/cac:Attachment)">[ibr-071]-An invoice should not include an AdditonalDocumentReference (ibg-24) simultaneously referring an Invoice Object Identifier (ibt-018) and an Attachment (ibt-125).</assert>
      <assert id="ibr-072" flag="fatal" test="not(//cac:AdditionalDocumentReference[cbc:DocumentTypeCode = '130']/cbc:DocumentDescription)">[ibr-072]-An invoice MUST not include an AdditionalDocumentReference (ibg-24) simultaneously referring an Invoice Object Identifier (ibt-018) and an Document Description (ibt-123).</assert>
      <assert id="ibr-076" flag="fatal" test="exists(cbc:ProfileID)">[ibr-076]-Business process (ibt-023) MUST be provided.</assert>
      <assert id="ibr-078" flag="fatal" test="(count(cac:AdditionalDocumentReference[cbc:DocumentTypeCode='130']) &lt;= 1)">[ibr-078]-Only one invoiced object (ibt-018) is allowed on document level.</assert>
      <assert id="ibr-084" flag="fatal" test="not(cbc:TaxCurrencyCode) or (cac:TaxTotal/cbc:TaxAmount[@currencyID=normalize-space(../../cbc:TaxCurrencyCode)] &lt;= 0 and cac:TaxTotal/cbc:TaxAmount[@currencyID=normalize-space(../../cbc:DocumentCurrencyCode)] &lt;= 0) or (cac:TaxTotal/cbc:TaxAmount[@currencyID=normalize-space(../../cbc:TaxCurrencyCode)] &gt;= 0 and cac:TaxTotal/cbc:TaxAmount[@currencyID=normalize-space(../../cbc:DocumentCurrencyCode)] &gt;= 0)">[ibr-084]-Invoice total tax amount (ibt-110) and Invoice total tax amount in accounting currency (ibt-111) MUST have the same operational sign.</assert>
      <assert id="ibr-087" flag="fatal" test="not(cac:Price/cbc:BaseQuantity) or xs:decimal(cac:Price/cbc:BaseQuantity) &gt; 0">[ibr-087]-Base quantity (ibt-149) MUST be a positive number above zero.</assert>
      <assert id="ibr-090" flag="fatal" test="(count(cac:ProjectReference) &lt;= 1) and (count(cac:AdditionalDocumentReference[cbc:DocumentTypeCode='50']) &lt;= 1)">[ibr-090]-Only one project reference (ibt-011) is allowed on document level.</assert>
      <assert id="ibr-094" flag="fatal" test="(count(cac:ContractDocumentReference/cbc:ID) &lt;= 1)">[ibr-094]-Contract reference (ibt-012) MUST occur maximum once.</assert>
      <assert id="ibr-095" flag="fatal" test="(count(cac:ReceiptDocumentReference/cbc:ID) &lt;= 1)">[ibr-095]-Receiving advice reference (ibt-015) MUST occur maximum once.</assert>
      <assert id="ibr-096" flag="fatal" test="(count(cac:DespatchDocumentReference/cbc:ID) &lt;= 1)">[ibr-096]-Despatch advice reference (ibt-016) MUST occur maximum once.</assert>
      <assert id="ibr-097" flag="fatal" test="(count(cac:InvoicePeriod) &lt;= 1)">[ibr-097]-Invoicing period (ibg-14) MUST occur maximum once.</assert>
      <assert id="ibr-098" flag="fatal" test="(count(cac:AccountingSupplierParty/cac:Party/cac:PartyLegalEntity/cbc:RegistrationName) &lt;= 1)">[ibr-098]-Seller name (ibt-027) MUST occur maximum once.</assert>
      <assert id="ibr-099" flag="fatal" test="(count(cac:AccountingSupplierParty/cac:Party/cac:PartyName/cbc:Name) &lt;= 1)">[ibr-099]-Seller trader name (ibt-028) MUST occur maximum once.</assert>
      <assert id="ibr-100" flag="fatal" test="(count(cac:AccountingSupplierParty/cac:Party/cac:PartyLegalEntity/cbc:CompanyID) &lt;= 1)">[ibr-100]-Seller legal registration identifier (ibt-030) MUST occur maximum once.</assert>
      <assert id="ibr-101" flag="fatal" test="(count(cac:AccountingSupplierParty/cac:Party/cac:PartyLegalEntity/cbc:CompanyLegalForm) &lt;= 1)">[ibr-101]-Seller additional legal information (ibt-033) MUST occur maximum once.</assert>
      <assert id="ibr-102" flag="fatal" test="(count(cac:AccountingCustomerParty/cac:Party/cac:PartyLegalEntity/cbc:RegistrationName) &lt;= 1)">[ibr-102]-Buyer name (ibt-044) MUST occur maximum once.</assert>
      <assert id="ibr-103" flag="fatal" test="(count(cac:AccountingCustomerParty/cac:Party/cac:PartyLegalEntity/cbc:CompanyID) &lt;= 1)">[ibr-103]-Buyer legal registration identifier (ibt-047) MUST occur maximum once.</assert>
      <assert id="ibr-104" flag="fatal" test="(count(cac:AccountingCustomerParty/cac:Party/cac:PartyTaxScheme/cbc:CompanyID) &lt;= 1)">[ibr-104]-Buyer tax identifier (ibt-048) MUST occur maximum once.</assert>
      <assert id="ibr-107" flag="fatal" test="(count(cac:Delivery) &lt;= 1)">[ibr-107]-Deliver to information (ibg-13) MUST occur maximum once.</assert>
      <assert id="ibr-112" flag="fatal" test="(count(cac:AccountingCustomerParty/cac:Party/cac:PartyName/cbc:Name) &lt;= 1)">[ibr-112]-Buyer trader name (ibt-045) MUST occur maximum once.</assert>
      <assert id="ibr-co-15" flag="fatal" test="cac:TaxTotal/cbc:TaxIncludedIndicator = true() or ((cac:LegalMonetaryTotal/xs:decimal(cbc:TaxInclusiveAmount) = round( (cac:LegalMonetaryTotal/xs:decimal(cbc:TaxExclusiveAmount) + cac:TaxTotal/xs:decimal(cbc:TaxAmount[@currencyID=/*/cbc:DocumentCurrencyCode])) * 10 * 10) div 100))">[ibr-co-15]-Invoice total amount with Tax (ibt-112) = Invoice total amount without Tax (ibt-109) + Invoice total Tax amount (ibt-110).</assert>
      <assert id="ibr-sr-16" flag="fatal" test="(count(cac:AccountingCustomerParty/cac:Party/cac:PartyIdentification/cbc:ID) &lt;= 1)">[ibr-sr-16]-Buyer identifier (ibt-046) MUST occur maximum once</assert>
      <assert id="ibr-sr-39" flag="fatal" test="(count(cac:ProjectReference/cbc:ID) &lt;= 1)">[ibr-sr-39]-Project reference (ibt-011) MUST occur maximum once.</assert>
      <assert id="ibr-sr-49" flag="fatal" test="(count(cac:InvoicePeriod/cbc:DescriptionCode) &lt;=1)">[ibr-sr-49]-Value tax point date (ibt-007) MUST occur maximum once</assert>
      <assert id="ibr-sr-51" flag="fatal" test="(count(cbc:Note) &lt;= 1)">[ibr-sr-51]-Note (ibt-022) MUST occur maximum once</assert>
      <assert id="ibr-sr-52" flag="fatal" test="(count(cac:OriginatorDocumentReference/cbc:ID) &lt;= 1)">[ibr-sr-52]-Tender or lot reference (ibt-017) MUST occur maximum once</assert>
    </rule>
    <rule context="cac:InvoiceLine | cac:CreditNoteLine">
      <assert id="ibr-021" flag="fatal" test="normalize-space(cbc:ID) != ''">[ibr-021]-Each Invoice line (ibg-25) MUST have an Invoice line identifier (ibt-126).</assert>
      <assert id="ibr-022" flag="fatal" test="exists(cbc:InvoicedQuantity) or exists(cbc:CreditedQuantity)">[ibr-022]-Each Invoice line (ibg-25) MUST have an invoiced quantity (ibt-129)..</assert>
      <assert id="ibr-023" flag="fatal" test="exists(cbc:InvoicedQuantity/@unitCode) or exists(cbc:CreditedQuantity/@unitCode)">[ibr-023]-An Invoice line (ibg-25) MUST have an Invoiced quantity unit of measure code (ibt-130).</assert>
      <assert id="ibr-024" flag="fatal" test="exists(cbc:LineExtensionAmount)">[ibr-024]-Each Invoice line (ibg-25) MUST have an Invoice line net amount (ibt-131).</assert>
      <assert id="ibr-025" flag="fatal" test="normalize-space(cac:Item/cbc:Name) != ''">[ibr-025]-Each Invoice line (ibg-25) MUST contain the Item name (ibt-153).</assert>
      <assert id="ibr-026" flag="fatal" test="exists(cac:Price/cbc:PriceAmount)">[ibr-026]-Each Invoice line (ibg-25) MUST contain the Item net price (ibt-146).</assert>
      <assert id="ibr-027" flag="fatal" test="(cac:Price/cbc:PriceAmount) &gt;= 0">[ibr-027]-The Item net price (ibt-146) MUST NOT be negative.</assert>
      <assert id="ibr-028" flag="fatal" test="(cac:Price/cac:AllowanceCharge/cbc:BaseAmount) &gt;= 0 or not(exists(cac:Price/cac:AllowanceCharge/cbc:BaseAmount))">[ibr-028]-The Item gross price (ibt-148) MUST NOT be negative.</assert>
      <assert id="ibr-089" flag="fatal" test="(count(cac:DocumentReference[cbc:DocumentTypeCode = 130]) &lt;= 1)">[ibr-089]-Only one invoiced object (ibt-128) is allowed per line (ibg-25).</assert>
      <assert id="ibr-109" flag="fatal" test="(count(cac:OrderLineReference/cbc:LineID) &lt;= 1)">[ibr-109]-Referenced purchase order line identifier (ibt-132) MUST occur maximum once.</assert>
      <assert id="ibr-110" flag="fatal" test="(count(cac:InvoicePeriod) &lt;= 1)">[ibr-110]-Invoice line period (ibg-26) MUST occur maximum once.</assert>
      <assert id="ibr-111" flag="fatal" test="(count(cac:Price/cac:AllowanceCharge/cbc:Amount) &lt;= 1)">[ibr-111]-Item price discount (ibt-147) MUST occur maximum once.</assert>
      <assert id="ibr-sr-34" flag="fatal" test="(count(cbc:Note) &lt;= 1)">[ibr-sr-34]-Invoice line note (ibt-127) MUST occur maximum once</assert>
      <assert id="ibr-sr-38" flag="fatal" test="(count(cac:Item/cac:ClassifiedTaxCategory/cbc:TaxExemptionReason) &lt;= 1)">[ibr-sr-38]-Invoiced item tax exemption reason text (ibt-185) MUST occur maximum once</assert>
      <assert id="ibr-sr-50" flag="fatal" test="(count(cac:Item/cbc:Description) &lt;= 1)">[ibr-sr-50]-Item description (ibt-154) MUST occur maximum once</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:InvoicePeriod | cac:CreditNoteLine/cac:InvoicePeriod">
      <assert id="ibr-085" flag="fatal" test="(cbc:StartDate &gt;= xs:date(../../cac:InvoicePeriod/cbc:StartDate)) or not(cbc:StartDate) or not(../../cac:InvoicePeriod/cbc:StartDate)">[ibr-085]-Start date of line period (ibt-134) MUST be within invoice period (ibg-14).</assert>
      <assert id="ibr-086" flag="fatal" test="(cbc:EndDate &lt;= xs:date(../../cac:InvoicePeriod/cbc:EndDate)) or not(cbc:EndDate) or not(../../cac:InvoicePeriod/cbc:EndDate)">[ibr-086]-End date of line period (ibt-135) MUST be within invoice period (ibg-14).</assert>
      <assert id="ibr-030" flag="fatal" test="(exists(cbc:EndDate) and exists(cbc:StartDate) and xs:date(cbc:EndDate) &gt;= xs:date(cbc:StartDate)) or not(exists(cbc:StartDate)) or not(exists(cbc:EndDate))">[ibr-030]-If both Invoice line period start date (ibt-134) and Invoice line period end date (ibt-135) are given then the Invoice line period end date (ibt-135) MUST be later or equal to the Invoice line period start date (ibt-134).</assert>
      <assert id="ibr-co-20" flag="fatal" test="exists(cbc:StartDate) or exists(cbc:EndDate)">[ibr-co-20]-If Invoice line period (ibg-26) is used, the Invoice line period start date (ibt-134) or the Invoice line period end date (ibt-135) MUST be filled, or both.</assert>
    </rule>
    <rule context="cac:InvoicePeriod">
      <assert id="ibr-029" flag="fatal" test="(exists(cbc:EndDate) and exists(cbc:StartDate) and xs:date(cbc:EndDate) &gt;= xs:date(cbc:StartDate)) or not(exists(cbc:StartDate)) or not(exists(cbc:EndDate))">[ibr-029]-If both Invoicing period start date (ibt-073) and Invoicing period end date (ibt-074) are given then the Invoicing period end date (ibt-074) MUST be later or equal to the Invoicing period start date (ibt-073).</assert>
      <assert id="ibr-co-19" flag="fatal" test="exists(cbc:StartDate) or exists(cbc:EndDate) or (exists(cbc:DescriptionCode) and not(exists(cbc:StartDate)) and not(exists(cbc:EndDate)))">[ibr-co-19]-If Invoicing period (ibg-14) is used, the Invoicing period start date (ibt-073) or the Invoicing period end date (ibt-074) MUST be filled, or both.</assert>
    </rule>
    <rule context="//cac:AdditionalItemProperty">
      <assert id="ibr-054" flag="fatal" test="exists(cbc:Name) and exists(cbc:Value)">[ibr-054]-Each Item attribute (ibg-32) MUST contain an Item attribute name (ibt-160) and an Item attribute value (ibt-161).</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:CommodityClassification/cbc:ItemClassificationCode | cac:CreditNoteLine/cac:Item/cac:CommodityClassification/cbc:ItemClassificationCode">
      <assert id="ibr-065" flag="fatal" test="exists(@listID)">[ibr-065]-The Item classification identifier (ibt-158) MUST have a Scheme identifier.</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:StandardItemIdentification/cbc:ID | cac:CreditNoteLine/cac:Item/cac:StandardItemIdentification/cbc:ID">
      <assert id="ibr-064" flag="fatal" test="exists(@schemeID)">[ibr-064]-The Item standard identifier (ibt-157) MUST have a Scheme identifier.</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:Item/cac:ClassifiedTaxCategory">
      <assert id="ibr-sr-58" flag="fatal" test="exists(cbc:ID)">[ibr-sr-58]-The Invoiced item TAX category code (ibt-151) MUST be present.</assert>
    </rule>
    <rule context="cac:PayeeParty">
      <assert id="ibr-017" flag="fatal" test="exists(cac:PartyName/cbc:Name) and (not(cac:PartyName/cbc:Name = ../cac:AccountingSupplierParty/cac:Party/cac:PartyName/cbc:Name) and not(cac:PartyIdentification/cbc:ID = ../cac:AccountingSupplierParty/cac:Party/cac:PartyIdentification/cbc:ID) )">[ibr-017]-The Payee name (ibt-059) MUST be provided in the Invoice, if the Payee (ibg-10) is different from the Seller (ibg-04).</assert>
      <assert id="ibr-105" flag="fatal" test="(count(cac:PartyIdentification/cbc:ID[upper-case(@schemeID) != 'SEPA']) &lt;= 1) or ((cac:PartyName/cbc:Name) = (../cac:AccountingSupplierParty/cac:Party/cac:PartyLegalEntity/cbc:RegistrationName))">[ibr-105]-The Payee identifier (ibt-060) MUST occur maximum once in the Invoice, if the Payee (ibg-10) is different from the Seller (ibg-04).</assert>
      <assert id="ibr-106" flag="fatal" test="(count(cac:PartyLegalEntity/cbc:CompanyID) &lt;= 1) or ((cac:PartyName/cbc:Name) = (../cac:AccountingSupplierParty/cac:Party/cac:PartyLegalEntity/cbc:RegistrationName))">[ibr-106]-The Payee legal identifier (ibt-061) MUST occur maximum once in the Invoice, if the Payee (ibg-10) is different from the Seller (ibg-04).</assert>
      <assert id="ibr-sr-19" flag="fatal" test="(count(cac:PartyName/cbc:Name) &lt;= 1) or ((cac:PartyName/cbc:Name) = (../cac:AccountingSupplierParty/cac:Party/cac:PartyLegalEntity/cbc:RegistrationName))">[ibr-sr-19]-Payee name (ibt-059) MUST occur maximum once, if the Payee is different from the Seller</assert>
    </rule>
    <rule context="cac:PaymentMeans/cac:PayeeFinancialAccount/cac:FinancialInstitutionBranch/cac:Address">
      <assert id="ibr-sr-59" flag="fatal" test="(count(cac:AddressLine/cbc:Line) &lt;= 1)">[ibr-sr-59]-The Payee Postal Address Line (ibt-174) MUST occur maximum once</assert>
    </rule>
    <rule context="cac:PaymentMeans">
      <assert id="ibr-049" flag="fatal" test="exists(cbc:PaymentMeansCode)">[ibr-049]-A Payment instruction (ibg-16) MUST specify the Payment means type code (ibt-081).</assert>
      <assert id="ibr-sr-27" flag="fatal" test="(count(cbc:PaymentMeansCode) &lt;= 1)">[ibr-sr-27]-Payment means text (ibt-081) MUST occur maximum once</assert>
      <assert id="ibr-sr-28" flag="fatal" test="(count(cac:PaymentMandate/cbc:ID) &lt;= 1)">[ibr-sr-28]-Mandate reference identifier (ibt-089) MUST occur maximum once</assert>
      <assert id="ibr-sr-45" flag="fatal" test="(count(cbc:PaymentDueDate) &lt;=1)">[ibr-sr-45]-Due Date (ibt-009) MUST occur maximum once</assert>
      <assert id="ibr-sr-46" flag="fatal" test="(count(cbc:PaymentMeansCode/@name) &lt;=1)">[ibr-sr-46]-Payment means text (ibt-082) MUST occur maximum once</assert>
    </rule>
    <rule context="cbc:Amount | cbc:BaseAmount | cbc:PriceAmount | cbc:LineExtensionAmount | cbc:TaxExclusiveAmount | cbc:TaxInclusiveAmount | cbc:AllowanceTotalAmount | cbc:ChargeTotalAmount | cbc:PrepaidAmount | cbc:PayableRoundingAmount | cbc:PayableAmount | cac:TaxTotal[cbc:TaxAmount/@currencyID=/*/cbc:DocumentCurrencyCode]/cbc:TaxAmount | cac:TaxTotal[cbc:TaxAmount/@currencyID=/*/cbc:DocumentCurrencyCode]/cac:TaxSubtotal/cbc:TaxableAmount | cac:TaxTotal[cbc:TaxAmount/@currencyID=/*/cbc:DocumentCurrencyCode]/cac:TaxSubtotal/cbc:TaxAmount">
      <assert id="ibr-126" flag="fatal" test="@currencyID = //cbc:DocumentCurrencyCode">[ibr-126]- All currencyID attributes must have the same value as the Invoice currency code (ibt-005), except for amounts expected to be in Tax accounting currency (ibt-006).</assert>
    </rule>
    <rule context="cac:PaymentTerms">
      <assert id="ibr-sr-05" flag="fatal" test="(count(cbc:Note) &lt;= 1)">[ibr-sr-05]-Payment terms (ibt-020) MUST occur maximum once.</assert>
      <assert id="ibr-sr-60" flag="fatal" test="(count(cbc:PaymentMeansID) &lt;= 1)">[ibr-sr-60]-Payment Instruction ID (ibt-187) MUST occur maximum once.</assert>
    </rule>
    <rule context="cac:BillingReference">
      <assert id="ibr-055" flag="fatal" test="exists(cac:InvoiceDocumentReference/cbc:ID)">[ibr-055]-Each Preceding Invoice reference (ibg-03) MUST contain a Preceding Invoice reference (ibt-025).</assert>
      <assert id="ibr-sr-07" flag="fatal" test="(cac:InvoiceDocumentReference/cbc:ID)">[ibr-sr-07]-If there is a preceding invoice reference (ibg-03) , the preceding invoice number (ibt-025) MUST be present</assert>
      <assert id="ibr-sr-06" flag="fatal" test="(count(cac:InvoiceDocumentReference) &lt;= 1)">[ibr-sr-06]-Preceding invoice reference (ibt-025) MUST occur maximum once</assert>
    </rule>
    <rule context="cac:AccountingSupplierParty/cac:Party">
      <assert id="ibr-co-26" flag="fatal" test="exists(cac:PartyTaxScheme/cbc:CompanyID) or exists(cac:PartyIdentification/cbc:ID) or exists(cac:PartyLegalEntity/cbc:CompanyID)">[ibr-co-26]-In order for the buyer to automatically identify a supplier, the Seller identifier (ibt-029), the Seller legal registration identifier (ibt-030) and/or the Seller Tax identifier (ibt-031) MUST be present.</assert>
      <assert id="ibr-081" flag="fatal" test="cbc:EndpointID">[ibr-081]-The Seller electronic address (ibt-049) MUST be provided.</assert>
      <assert id="ibr-sr-42" flag="fatal" test="(count(cac:PartyTaxScheme) &lt;= 2)">[ibr-sr-42]-Party tax scheme MUST occur maximum twice in accounting supplier party</assert>
    </rule>
    <rule context="cac:AccountingSupplierParty/cac:Party/cbc:EndpointID">
      <assert id="ibr-062" flag="fatal" test="exists(@schemeID)">[ibr-062]-The Seller electronic address (ibt-034) MUST have a Scheme identifier.</assert>
    </rule>
    <rule context="cac:AccountingSupplierParty/cac:Party/cac:PostalAddress">
      <assert id="ibr-009" flag="fatal" test="normalize-space(cac:Country/cbc:IdentificationCode) != ''">[ibr-009]-The Seller postal address (ibg-05) MUST contain a Seller country code (ibt-040).</assert>
      <assert id="ibr-sr-53" flag="fatal" test="(count(cac:AddressLine/cbc:Line) &lt;= 1)">[ibr-sr-53]-The Seller Postal Address Line (ibt-162) MUST occur maximum once</assert>
    </rule>
    <rule context="cac:TaxRepresentativeParty">
      <assert id="ibr-018" flag="fatal" test="normalize-space(cac:PartyName/cbc:Name) != ''">[ibr-018]-The Seller tax representative name (ibt-062) MUST be provided in the Invoice, if the Seller (ibg-04) has a Seller tax representative party (ibg-11)</assert>
      <assert id="ibr-019" flag="fatal" test="exists(cac:PostalAddress)">[ibr-019]-The Seller tax representative postal address (ibg-12) MUST be provided in the Invoice, if the Seller (ibg-04) has a Seller tax representative party (ibg-11).</assert>
      <assert id="ibr-056" flag="fatal" test="exists(cac:PartyTaxScheme/cbc:CompanyID)">[ibr-056]-The Seller tax representative party (ibg-11) MUST have a Seller tax representative Tax identifier (ibt-063).</assert>
      <assert id="ibr-sr-22" flag="fatal" test="(count(cac:PartyName/cbc:Name) &lt;= 1)">[ibr-sr-22]-Seller tax representative name (ibt-062) MUST occur maximum once, if the Seller has a tax representative</assert>
      <assert id="ibr-sr-23" flag="fatal" test="(count(cac:Party/cac:PartyTaxScheme/cbc:CompanyID) &lt;= 1)">[ibr-sr-23]-Seller tax representative tax identifier (ibt-063) MUST occur maximum once, if the Seller has a tax representative</assert>
    </rule>
    <rule context="cac:TaxRepresentativeParty/cac:PostalAddress">
      <assert id="ibr-020" flag="fatal" test="normalize-space(cac:Country/cbc:IdentificationCode) != ''">[ibr-020]-The Seller tax representative postal address (ibg-12) MUST contain a Tax representative country code (ibt-069), if the Seller (ibg-04) has a Seller tax representative party (ibg-11).</assert>
      <assert id="ibr-sr-55" flag="fatal" test="(count(cac:AddressLine/cbc:Line) &lt;= 1)">[ibr-sr-55]-The Tax Representative Postal Address Line (ibt-164) MUST occur maximum once</assert>
    </rule>
    <rule context="/ubl:Invoice/cac:TaxTotal | /cn:CreditNote/cac:Taxtotal">
      <assert id="ibr-124" flag="fatal" test="string-length(substring-after(cbc:TaxAmount, '.')) &lt;= 2">[ibr-124]-Invoice total TAX amount (ibt-110) MUST have no more than 2 decimals.</assert>
      <assert id="ibr-co-14" flag="fatal" test="(xs:decimal(child::cbc:TaxAmount)= round((sum(cac:TaxSubtotal/xs:decimal(cbc:TaxAmount)) * 10 * 10)) div 100) or not(cac:TaxSubtotal)">[ibr-co-14]-Invoice total Tax amount (ibt-110) = Σ Tax category tax amount (ibt-117).</assert>
    </rule>
    <rule context="//cac:TaxSubtotal">
      <assert id="ibr-sr-32" flag="fatal" test="(count(cac:TaxCategory/cbc:TaxExemptionReason) &lt;= 1)">[ibr-sr-32]-VAT exemption reason text (ibt-120) MUST occur maximum once.</assert>
    </rule>
    <rule context="//cac:PartyTaxScheme">
      <assert id="ibr-sr-57" flag="fatal" test="exists(cbc:CompanyID)">[ibr-sr-57]-Company identifier MUST exist in the party tax scheme class.</assert>
    </rule>
    <rule context="cbc:IssueDate | cbc:DueDate | cbc:TaxPointDate | cbc:StartDate | cbc:EndDate | cbc:ActualDeliveryDate">
      <assert id="ibr-073" flag="fatal" test="string-length(text()) = 10 and (string(.) castable as xs:date)">[ibr-073]-A date MUST be formatted YYYY-MM-DD in (ibt-002), (ibt-007), (ibt-009), (ibt-026), (ibt-072), (ibt-073), (ibt-074), (ibt-134), (ibt-135).</assert>
    </rule>
    <rule context="//*[namespace-uri()='urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2' and ends-with(local-name(), 'Time')]">
      <assert id="ibr-119" flag="fatal" test="(string(.) castable as xs:time)">[ibr-119]-A time MUST be formatted hh:mm:ss with optional fractional seconds .sss and time zone expression (ibt-168).</assert>
    </rule>
    <rule context="//*[ends-with(name(), 'BinaryObject')]">
      <assert id="ibr-074" flag="fatal" test="(@mimeCode)">[ibr-074]-Binary object elements (ibt-125) MUST contain the mime code attribute (ibt-125-1).</assert>
      <assert id="ibr-075" flag="fatal" test="(@filename)">[ibr-075]-Binary object elements (ibt-125) MUST contain the filename attribute (ibt-125-2).</assert>
    </rule>
    <rule context="cbc:TaxCurrencyCode">
      <assert id="ibr-077" flag="fatal" test="not(normalize-space(text()) = normalize-space(../cbc:DocumentCurrencyCode/text()))">[ibr-077]-Tax accounting currency code (ibt-006) MUST be different from invoice currency code (ibt-005) when provided.</assert>
    </rule>
    <rule context="//*[not(*) and not(normalize-space())]">
      <assert id="ibr-079" flag="fatal" test="false()">[ibr-079]-Document MUST not contain empty elements.</assert>
    </rule>
    <rule context="cac:Price/cbc:BaseQuantity[@unitCode]">
      <assert id="ibr-088" flag="fatal" test="not(../../cbc:InvoicedQuantity or ../../cbc:CreditedQuantity) or (@unitCode = ../../cbc:InvoicedQuantity/@unitCode) or (@unitCode = ../../cbc:CreditedQuantity/@unitCode)">[ibr-088]-Unit code of price base quantity (ibt-150) MUST be same as invoiced quantity (ibt-130).</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:DespatchLineReference/cac:DocumentReference/cbc:ID | cac:CreditNoteLine/cac:DespatchLineReference/cac:DocumentReference/cbc:ID">
      <assert id="ibr-092" flag="fatal" test="count(//cac:DespatchDocumentReference) = 0">[ibr-092]-If despatch advice reference exists on line (ibt-184) then there MUST not be despatch reference on document level (ibt-016).</assert>
    </rule>
    <rule context="cac:InvoiceLine/cac:DespatchLineReference | cac:CreditNoteLine/cac:DespatchLineReference">
      <assert id="ibr-sr-62" flag="fatal" test="(count(cac:DocumentReference) &lt;= 1)">[ibr-sr-62]-Despatch advice line reference (ibt-184) MUST occur maximum once.</assert>
    </rule>
    <rule context="cac:PrepaidPayment[1]">
      <assert id="ibr-093" flag="fatal" test="exists(../cac:LegalMonetaryTotal/cbc:PrepaidAmount)">[ibr-093]-If there is a paid amount (ibt-180) then total paid amount (ibt-113) MUST exist.</assert>
    </rule>
    <rule context="cac:Delivery">
      <assert id="ibr-108" flag="fatal" test="(count(cac:DeliveryParty/cac:PartyName/cbc:Name) &lt;= 1)">[ibr-108]-Deliver to party name (ibt-070) MUST occur maximum once.</assert>
    </rule>
  </pattern>
  <pattern id="Codesmodel">
    <rule flag="fatal" context="cbc:InvoiceTypeCode | cbc:CreditNoteTypeCode">
      <assert id="ibr-cl-01" flag="fatal" test="(self::cbc:InvoiceTypeCode and ((not(contains(normalize-space(.), ' ')) and contains(' 80 82 84 380 383 *********** *********** 780 71 *********** *********** *********** *********** ', concat(' ', normalize-space(.), ' '))))) or (self::cbc:CreditNoteTypeCode and ((not(contains(normalize-space(.), ' ')) and contains(' 81 83 *********** ', concat(' ', normalize-space(.), ' ')))))">[ibr-cl-01]-The document type code  (ibt-003) MUST be coded by the invoice and credit note related code lists of UNTDID 1001.</assert>
    </rule>
    <rule flag="fatal" context="cbc:Amount | cbc:BaseAmount | cbc:PriceAmount | cbc:TaxAmount | cbc:TaxableAmount | cbc:LineExtensionAmount | cbc:TaxExclusiveAmount | cbc:TaxInclusiveAmount | cbc:AllowanceTotalAmount | cbc:ChargeTotalAmount | cbc:PrepaidAmount | cbc:PayableRoundingAmount | cbc:PayableAmount">
      <assert id="ibr-cl-03" flag="fatal" test="((not(contains(normalize-space(@currencyID), ' ')) and contains(' AED AFN ALL AMD ANG AOA ARS AUD AWG AZN BAM BBD BDT BGN BHD BIF BMD BND BOB BOV BRL BSD BTN BWP BYN BZD CAD CDF CHE CHF CHW CLF CLP CNY COP COU CRC CUC CUP CVE CZK DJF DKK DOP DZD EGP ERN ETB EUR FJD FKP GBP GEL GHS GIP GMD GNF GTQ GYD HKD HNL HRK HTG HUF IDR ILS INR IQD IRR ISK JMD JOD JPY KES KGS KHR KMF KPW KRW KWD KYD KZT LAK LBP LKR LRD LSL LYD MAD MDL MGA MKD MMK MNT MOP MRU MUR MVR MWK MXN MXV MYR MZN NAD NGN NIO NOK NPR NZD OMR PAB PEN PGK PHP PKR PLN PYG QAR RON RSD RUB RWF SAR SBD SCR SDG SEK SGD SHP SOS SRD SSP STN SVC SYP SZL THB TJS TMT TND TOP TRY TTD TWD TZS UAH UGX USD USN UYI UYU UZS VEF VND VUV WST XAF XAG XAU XBA XBB XBC XBD XCD XDR XOF XPD XPF XPT XSU XTS XUA XXX YER ZAR ZMW ZWL ', concat(' ', normalize-space(@currencyID), ' '))))">[ibr-cl-03]-currencyID MUST be coded using ISO code list 4217 alpha-3.</assert>
    </rule>
    <rule flag="fatal" context="cbc:DocumentCurrencyCode">
      <assert id="ibr-cl-04" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' AED AFN ALL AMD ANG AOA ARS AUD AWG AZN BAM BBD BDT BGN BHD BIF BMD BND BOB BOV BRL BSD BTN BWP BYN BZD CAD CDF CHE CHF CHW CLF CLP CNY COP COU CRC CUC CUP CVE CZK DJF DKK DOP DZD EGP ERN ETB EUR FJD FKP GBP GEL GHS GIP GMD GNF GTQ GYD HKD HNL HRK HTG HUF IDR ILS INR IQD IRR ISK JMD JOD JPY KES KGS KHR KMF KPW KRW KWD KYD KZT LAK LBP LKR LRD LSL LYD MAD MDL MGA MKD MMK MNT MOP MRU MUR MVR MWK MXN MXV MYR MZN NAD NGN NIO NOK NPR NZD OMR PAB PEN PGK PHP PKR PLN PYG QAR RON RSD RUB RWF SAR SBD SCR SDG SEK SGD SHP SOS SRD SSP STN SVC SYP SZL THB TJS TMT TND TOP TRY TTD TWD TZS UAH UGX USD USN UYI UYU UZS VEF VND VUV WST XAF XAG XAU XBA XBB XBC XBD XCD XDR XOF XPD XPF XPT XSU XTS XUA XXX YER ZAR ZMW ZWL ', concat(' ', normalize-space(.), ' '))))">[ibr-cl-04]-Invoice currency code  (ibt-005) MUST be coded using ISO code list 4217 alpha-3</assert>
    </rule>
    <rule flag="fatal" context="cbc:TaxCurrencyCode">
      <assert id="ibr-cl-05" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' AED AFN ALL AMD ANG AOA ARS AUD AWG AZN BAM BBD BDT BGN BHD BIF BMD BND BOB BOV BRL BSD BTN BWP BYN BZD CAD CDF CHE CHF CHW CLF CLP CNY COP COU CRC CUC CUP CVE CZK DJF DKK DOP DZD EGP ERN ETB EUR FJD FKP GBP GEL GHS GIP GMD GNF GTQ GYD HKD HNL HRK HTG HUF IDR ILS INR IQD IRR ISK JMD JOD JPY KES KGS KHR KMF KPW KRW KWD KYD KZT LAK LBP LKR LRD LSL LYD MAD MDL MGA MKD MMK MNT MOP MRU MUR MVR MWK MXN MXV MYR MZN NAD NGN NIO NOK NPR NZD OMR PAB PEN PGK PHP PKR PLN PYG QAR RON RSD RUB RWF SAR SBD SCR SDG SEK SGD SHP SOS SRD SSP STN SVC SYP SZL THB TJS TMT TND TOP TRY TTD TWD TZS UAH UGX USD USN UYI UYU UZS VEF VND VUV WST XAF XAG XAU XBA XBB XBC XBD XCD XDR XOF XPD XPF XPT XSU XTS XUA XXX YER ZAR ZMW ZWL ', concat(' ', normalize-space(.), ' '))))">[ibr-cl-05]-Tax currency code  (ibt-006) MUST be coded using ISO code list 4217 alpha-3</assert>
    </rule>
    <rule flag="fatal" context="cac:AdditionalDocumentReference[cbc:DocumentTypeCode = '130']/cbc:ID[@schemeID] | cac:DocumentReference[cbc:DocumentTypeCode = '130']/cbc:ID[@schemeID]">
      <assert id="ibr-cl-07" flag="fatal" test="((not(contains(normalize-space(@schemeID), ' ')) and contains(' AAA AAB AAC AAD AAE AAF AAG AAH AAI AAJ AAK AAL AAM AAN AAO AAP AAQ AAR AAS AAT AAU AAV AAW AAX AAY AAZ ABA ABB ABC ABD ABE ABF ABG ABH ABI ABJ ABK ABL ABM ABN ABO ABP ABQ ABR ABS ABT ABU ABV ABW ABX ABY ABZ AC ACA ACB ACC ACD ACE ACF ACG ACH ACI ACJ ACK ACL ACN ACO ACP ACQ ACR ACT ACU ACV ACW ACX ACY ACZ ADA ADB ADC ADD ADE ADF ADG ADI ADJ ADK ADL ADM ADN ADO ADP ADQ ADT ADU ADV ADW ADX ADY ADZ AE AEA AEB AEC AED AEE AEF AEG AEH AEI AEJ AEK AEL AEM AEN AEO AEP AEQ AER AES AET AEU AEV AEW AEX AEY AEZ AF AFA AFB AFC AFD AFE AFF AFG AFH AFI AFJ AFK AFL AFM AFN AFO AFP AFQ AFR AFS AFT AFU AFV AFW AFX AFY AFZ AGA AGB AGC AGD AGE AGF AGG AGH AGI AGJ AGK AGL AGM AGN AGO AGP AGQ AGR AGS AGT AGU AGV AGW AGX AGY AGZ AHA AHB AHC AHD AHE AHF AHG AHH AHI AHJ AHK AHL AHM AHN AHO AHP AHQ AHR AHS AHT AHU AHV AHX AHY AHZ AIA AIB AIC AID AIE AIF AIG AIH AII AIJ AIK AIL AIM AIN AIO AIP AIQ AIR AIS AIT AIU AIV AIW AIX AIY AIZ AJA AJB AJC AJD AJE AJF AJG AJH AJI AJJ AJK AJL AJM AJN AJO AJP AJQ AJR AJS AJT AJU AJV AJW AJX AJY AJZ AKA AKB AKC AKD AKE AKF AKG AKH AKI AKJ AKK AKL AKM AKN AKO AKP AKQ AKR AKS AKT AKU AKV AKW AKX AKY AKZ ALA ALB ALC ALD ALE ALF ALG ALH ALI ALJ ALK ALL ALM ALN ALO ALP ALQ ALR ALS ALT ALU ALV ALW ALX ALY ALZ AMA AMB AMC AMD AME AMF AMG AMH AMI AMJ AMK AML AMM AMN AMO AMP AMQ AMR AMS AMT AMU AMV AMW AMX AMY AMZ ANA ANB ANC AND ANE ANF ANG ANH ANI ANJ ANK ANL ANM ANN ANO ANP ANQ ANR ANS ANT ANU ANV ANW ANX ANY AOA AOD AOE AOF AOG AOH AOI AOJ AOK AOL AOM AON AOO AOP AOQ AOR AOS AOT AOU AOV AOW AOX AOY AOZ AP APA APB APC APD APE APF APG APH API APJ APK APL APM APN APO APP APQ APR APS APT APU APV APW APX APY APZ AQA AQB AQC AQD AQE AQF AQG AQH AQI AQJ AQK AQL AQM AQN AQO AQP AQQ AQR AQS AQT AQU AQV AQW AQX AQY AQZ ARA ARB ARC ARD ARE ARF ARG ARH ARI ARJ ARK ARL ARM ARN ARO ARP ARQ ARR ARS ART ARU ARV ARW ARX ARY ARZ ASA ASB ASC ASD ASE ASF ASG ASH ASI ASJ ASK ASL ASM ASN ASO ASP ASQ ASR ASS AST ASU ASV ASW ASX ASY ASZ ATA ATB ATC ATD ATE ATF ATG ATH ATI ATJ ATK ATL ATM ATN ATO ATP ATQ ATR ATS ATT ATU ATV ATW ATX ATY ATZ AU AUA AUB AUC AUD AUE AUF AUG AUH AUI AUJ AUK AUL AUM AUN AUO AUP AUQ AUR AUS AUT AUU AUV AUW AUX AUY AUZ AV AVA AVB AVC AVD AVE AVF AVG AVH AVI AVJ AVK AVL AVM AVN AVO AVP AVQ AVR AVS AVT AVU AVV AVW AVX AVY AVZ AWA AWB AWC AWD AWE AWF AWG AWH AWI AWJ AWK AWL AWM AWN AWO AWP AWQ AWR AWS AWT AWU AWV AWW AWX AWY AWZ AXA AXB AXC AXD AXE AXF AXG AXH AXI AXJ AXK AXL AXM AXN AXO AXP AXQ AXR AXS BA BC BD BE BH BM BN BO BR BT BTP BW CAS CAT CAU CAV CAW CAX CAY CAZ CBA CBB CD CEC CED CFE CFF CFO CG CH CK CKN CM CMR CN CNO COF CP CR CRN CS CST CT CU CV CW CZ DA DAN DB DI DL DM DQ DR EA EB ED EE EEP EI EN EQ ER ERN ET EX FC FF FI FLW FN FO FS FT FV FX GA GC GD GDN GN HS HWB IA IB ICA ICE ICO II IL INB INN INO IP IS IT IV JB JE LA LAN LAR LB LC LI LO LRC LS MA MB MF MG MH MR MRN MS MSS MWB NA NF OH OI ON OP OR PB PC PD PE PF PI PK PL POR PP PQ PR PS PW PY RA RC RCN RE REN RF RR RT SA SB SD SE SEA SF SH SI SM SN SP SQ SRN SS STA SW SZ TB TCR TE TF TI TIN TL TN TP UAR UC UCN UN UO URI VA VC VGR VM VN VON VOR VP VR VS VT VV WE WM WN WR WS WY XA XC XP ZZZ ', concat(' ', normalize-space(@schemeID), ' '))))">[ibr-cl-07]-Object identifier identification scheme (ibt-018-1) (ibt-128-1) MUST be coded using a restriction of UNTDID 1153.</assert>
    </rule>
    <rule flag="fatal" context="cac:PartyIdentification/cbc:ID[@schemeID]">
      <assert id="ibr-cl-10" flag="fatal" test="((not(contains(normalize-space(@schemeID), ' ')) and contains(' 0002 0003 0004 0005 0006 0007 0008 0009 0010 0011 0012 0013 0014 0015 0016 0017 0018 0019 0020 0021 0022 0023 0024 0025 0026 0027 0028 0029 0030 0031 0032 0033 0034 0035 0036 0037 0038 0039 0040 0041 0042 0043 0044 0045 0046 0047 0048 0049 0050 0051 0052 0053 0054 0055 0056 0057 0058 0059 0060 0061 0062 0063 0064 0065 0066 0067 0068 0069 0070 0071 0072 0073 0074 0075 0076 0077 0078 0079 0080 0081 0082 0083 0084 0085 0086 0087 0088 0089 0090 0091 0093 0094 0095 0096 0097 0098 0099 0100 0101 0102 0104 0105 0106 0107 0108 0109 0110 0111 0112 0113 0114 0115 0116 0117 0118 0119 0120 0121 0122 0123 0124 0125 0126 0127 0128 0129 0130 0131 0132 0133 0134 0135 0136 0137 0138 0139 0140 0141 0142 0143 0144 0145 0146 0147 0148 0149 0150 0151 0152 0153 0154 0155 0156 0157 0158 0159 0160 0161 0162 0163 0164 0165 0166 0167 0168 0169 0170 0171 0172 0173 0174 0175 0176 0177 0178 0179 0180 0183 0184 0185 0186 0187 0188 0189 0190 0191 0192 0193 0194 0195 0196 0197 0198 0199 0200 0201 0202 0203 0204 0205 0206 0207 0208 0209 0210 0211 0212 0213 0214 0215 0216 0217 0218 0219 0220 0221 0222 0223 0224 0225 0226 0227 0228 0229 0230 ', concat(' ', normalize-space(@schemeID), ' '))))  or ((not(contains(normalize-space(@schemeID), ' ')) and contains(' SEPA ', concat(' ', normalize-space(@schemeID), ' '))) and ((ancestor::cac:AccountingSupplierParty) or (ancestor::cac:PayeeParty)))">[ibr-cl-10]-Any identifier identification scheme (ibt-046-1), (ibt-060-1) MUST be coded using one of the ISO 6523 ICD list.</assert>
    </rule>
    <rule flag="fatal" context="cac:PartyLegalEntity/cbc:CompanyID[@schemeID]">
      <assert id="ibr-cl-11" flag="fatal" test="((not(contains(normalize-space(@schemeID), ' ')) and contains(' 0002 0003 0004 0005 0006 0007 0008 0009 0010 0011 0012 0013 0014 0015 0016 0017 0018 0019 0020 0021 0022 0023 0024 0025 0026 0027 0028 0029 0030 0031 0032 0033 0034 0035 0036 0037 0038 0039 0040 0041 0042 0043 0044 0045 0046 0047 0048 0049 0050 0051 0052 0053 0054 0055 0056 0057 0058 0059 0060 0061 0062 0063 0064 0065 0066 0067 0068 0069 0070 0071 0072 0073 0074 0075 0076 0077 0078 0079 0080 0081 0082 0083 0084 0085 0086 0087 0088 0089 0090 0091 0093 0094 0095 0096 0097 0098 0099 0100 0101 0102 0104 0105 0106 0107 0108 0109 0110 0111 0112 0113 0114 0115 0116 0117 0118 0119 0120 0121 0122 0123 0124 0125 0126 0127 0128 0129 0130 0131 0132 0133 0134 0135 0136 0137 0138 0139 0140 0141 0142 0143 0144 0145 0146 0147 0148 0149 0150 0151 0152 0153 0154 0155 0156 0157 0158 0159 0160 0161 0162 0163 0164 0165 0166 0167 0168 0169 0170 0171 0172 0173 0174 0175 0176 0177 0178 0179 0180 0183 0184 0185 0186 0187 0188 0189 0190 0191 0192 0193 0194 0195 0196 0197 0198 0199 0200 0201 0202 0203 0204 0205 0206 0207 0208 0209 0210 0211 0212 0213 0214 0215 0216 0217 0218 0219 0220 0221 0222 0223 0224 0225 0226 0227 0228 0229 0230 ', concat(' ', normalize-space(@schemeID), ' '))))">[ibr-cl-11]-Any registration identifier identification scheme (ibt-030-1), (ibt-047-1), (ibt-061-1) MUST be coded using one of the ISO 6523 ICD list.</assert>
    </rule>
    <rule flag="fatal" context="cac:CommodityClassification/cbc:ItemClassificationCode[@listID]">
      <assert id="ibr-cl-13" flag="fatal" test="((not(contains(normalize-space(@listID), ' ')) and contains(' AA AB AC AD AE AF AG AH AI AJ AK AL AM AN AO AP AQ AR AS AT AU AV AW AX AY AZ BA BB BC BD BE BF BG BH BI BJ BK BL BM BN BO BP BQ BR BS BT BU BV BW BX BY BZ CC CG CL CR CV DR DW EC EF EN EMD FS GB GMN GN GS HS IB IN IS IT IZ MA MF MN MP NB ON PD PL PO PV QS RC RN RU RY SA SG SK SN SRS SRT SRU SRV SRW SRX SRY SRZ SS SSA SSB SSC SSD SSE SSF SSG SSH SSI SSJ SSK SSL SSM SSN SSO SSP SSQ SSR SSS SST SSU SSV SSW SSX SSY SSZ ST STA STB STC STD STE STF STG STH STI STJ STK STL STM STN STO STP STQ STR STS STT STU STV STW STX STY STZ SUA SUB SUC SUD SUE SUF SUG SUH SUI SUJ SUK SUL SUM TG TSN TSO TSP TSQ TSR TSS TST TSU UA UP VN VP VS VX ZZZ ', concat(' ', normalize-space(@listID), ' '))))">[ibr-cl-13]-Item classification identifier identification scheme (ibt-158-1) MUST be coded using one of the UNTDID 7143 list.</assert>
    </rule>
    <rule flag="fatal" context="cac:Country/cbc:IdentificationCode">
      <assert id="ibr-cl-14" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' 1A AD AE AF AG AI AL AM AO AQ AR AS AT AU AW AX AZ BA BB BD BE BF BG BH BI BJ BL BM BN BO BQ BR BS BT BV BW BY BZ CA CC CD CF CG CH CI CK CL CM CN CO CR CU CV CW CX CY CZ DE DJ DK DM DO DZ EC EE EG EH ER ES ET FI FJ FK FM FO FR GA GB GD GE GF GG GH GI GL GM GN GP GQ GR GS GT GU GW GY HK HM HN HR HT HU ID IE IL IM IN IO IQ IR IS IT JE JM JO JP KE KG KH KI KM KN KP KR KW KY KZ LA LB LC LI LK LR LS LT LU LV LY MA MC MD ME MF MG MH MK ML MM MN MO MP MQ MR MS MT MU MV MW MX MY MZ NA NC NE NF NG NI NL NO NP NR NU NZ OM PA PE PF PG PH PK PL PM PN PR PS PT PW PY QA RE RO RS RU RW SA SB SC SD SE SG SH SI SJ SK SL SM SN SO SR SS ST SV SX SY SZ TC TD TF TG TH TJ TK TL TM TN TO TR TT TV TW TZ UA UG UM US UY UZ VA VC VE VG VI VN VU WF WS XI YE YT ZA ZM ZW ', concat(' ', normalize-space(.), ' '))))">[ibr-cl-14]-Country codes in an invoice (ibt-040), (ibt-055), (ibt-069), (ibt-080), (ibt-175) MUST be coded using ISO code list 3166-1</assert>
    </rule>
    <rule flag="fatal" context="cac:OriginCountry/cbc:IdentificationCode">
      <assert id="ibr-cl-15" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' 1A AD AE AF AG AI AL AM AO AQ AR AS AT AU AW AX AZ BA BB BD BE BF BG BH BI BJ BL BM BN BO BQ BR BS BT BV BW BY BZ CA CC CD CF CG CH CI CK CL CM CN CO CR CU CV CW CX CY CZ DE DJ DK DM DO DZ EC EE EG EH ER ES ET FI FJ FK FM FO FR GA GB GD GE GF GG GH GI GL GM GN GP GQ GR GS GT GU GW GY HK HM HN HR HT HU ID IE IL IM IN IO IQ IR IS IT JE JM JO JP KE KG KH KI KM KN KP KR KW KY KZ LA LB LC LI LK LR LS LT LU LV LY MA MC MD ME MF MG MH MK ML MM MN MO MP MQ MR MS MT MU MV MW MX MY MZ NA NC NE NF NG NI NL NO NP NR NU NZ OM PA PE PF PG PH PK PL PM PN PR PS PT PW PY QA RE RO RS RU RW SA SB SC SD SE SG SH SI SJ SK SL SM SN SO SR SS ST SV SX SY SZ TC TD TF TG TH TJ TK TL TM TN TO TR TT TV TW TZ UA UG UM US UY UZ VA VC VE VG VI VN VU WF WS XI YE YT ZA ZM ZW ', concat(' ', normalize-space(.), ' '))))">[ibr-cl-15]-Origin country codes in an invoice (ibt-159) MUST be coded using ISO code list 3166-1</assert>
    </rule>
    <rule flag="fatal" context="cac:PaymentMeans/cbc:PaymentMeansCode">
      <assert id="ibr-cl-16" flag="fatal" test="( ( not(contains(normalize-space(.),' ')) and contains( ' 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49 50 51 52 53 54 55 56 57 58 59 60 61 62 63 64 65 66 67 68 70 74 75 76 77 78 91 92 93 94 95 96 97 98 ZZZ Z01 Z02 Z03 Z04 Z05 Z06 Z07 Z08 ',concat(' ',normalize-space(.),' ') ) ) )">[ibr-cl-16]-Payment means in an invoice (ibt-081) MUST be coded using UNCL4461 code list</assert>
    </rule>
    <rule flag="fatal" context="cac:AllowanceCharge[cbc:ChargeIndicator = false()]/cbc:AllowanceChargeReasonCode">
      <assert id="ibr-cl-19" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' 41 42 60 62 63 64 65 66 67 68 70 71 88 ************** 104 105 ', concat(' ', normalize-space(.), ' '))))">[ibr-cl-19]-Coded allowance reasons (ibt-098), (ibt-140) MUST belong to the UNCL 5189 code list</assert>
    </rule>
    <rule flag="fatal" context="cac:AllowanceCharge[cbc:ChargeIndicator = true()]/cbc:AllowanceChargeReasonCode">
      <assert id="ibr-cl-20" flag="fatal" test="((not(contains(normalize-space(.), ' ')) and contains(' AA AAA AAC AAD AAE AAF AAH AAI AAS AAT AAV AAY AAZ ABA ABB ABC ABD ABF ABK ABL ABN ABR ABS ABT ABU ACF ACG ACH ACI ACJ ACK ACL ACM ACS ADC ADE ADJ ADK ADL ADM ADN ADO ADP ADQ ADR ADT ADW ADY ADZ AEA AEB AEC AED AEF AEH AEI AEJ AEK AEL AEM AEN AEO AEP AES AET AEU AEV AEW AEX AEY AEZ AJ AU CA CAB CAD CAE CAF CAI CAJ CAK CAL CAM CAN CAO CAP CAQ CAR CAS CAT CAU CAV CAW CAX CAY CAZ CD CG CS CT DAB DAD DAC DAF DAG DAH DAI DAJ DAK DAL DAM DAN DAO DAP DAQ DL EG EP ER FAA FAB FAC FC FH FI GAA HAA HD HH IAA IAB ID IF IR IS KO L1 LA LAA LAB LF MAE MI ML NAA OA PA PAA PC PL PRV RAB RAC RAD RAF RE RF RH RV SA SAA SAD SAE SAI SG SH SM SU TAB TAC TT TV V1 V2 WH XAA YY ZZZ ', concat(' ', normalize-space(.), ' '))))">[ibr-cl-20]-Coded charge reasons (ibt-105), (ibt-145) MUST belong to the UNCL 7161 code list</assert>
    </rule>
    <rule flag="fatal" context="cac:StandardItemIdentification/cbc:ID[@schemeID]">
      <assert id="ibr-cl-21" flag="fatal" test="((not(contains(normalize-space(@schemeID), ' ')) and contains(' 0002 0003 0004 0005 0006 0007 0008 0009 0010 0011 0012 0013 0014 0015 0016 0017 0018 0019 0020 0021 0022 0023 0024 0025 0026 0027 0028 0029 0030 0031 0032 0033 0034 0035 0036 0037 0038 0039 0040 0041 0042 0043 0044 0045 0046 0047 0048 0049 0050 0051 0052 0053 0054 0055 0056 0057 0058 0059 0060 0061 0062 0063 0064 0065 0066 0067 0068 0069 0070 0071 0072 0073 0074 0075 0076 0077 0078 0079 0080 0081 0082 0083 0084 0085 0086 0087 0088 0089 0090 0091 0093 0094 0095 0096 0097 0098 0099 0100 0101 0102 0104 0105 0106 0107 0108 0109 0110 0111 0112 0113 0114 0115 0116 0117 0118 0119 0120 0121 0122 0123 0124 0125 0126 0127 0128 0129 0130 0131 0132 0133 0134 0135 0136 0137 0138 0139 0140 0141 0142 0143 0144 0145 0146 0147 0148 0149 0150 0151 0152 0153 0154 0155 0156 0157 0158 0159 0160 0161 0162 0163 0164 0165 0166 0167 0168 0169 0170 0171 0172 0173 0174 0175 0176 0177 0178 0179 0180 0183 0184 0185 0186 0187 0188 0189 0190 0191 0192 0193 0194 0195 0196 0197 0198 0199 0200 0201 0202 0203 0204 0205 0206 0207 0208 0209 0210 0211 0212 0213 0214 0215 0216 0217 0218 0219 0220 0221 0222 0223 0224 0225 0226 0227 0228 0229 0230 ', concat(' ', normalize-space(@schemeID), ' '))))">[ibr-cl-21]-Item standard identifier scheme identifier (ibt-157) MUST belong to the ISO 6523 ICD list.</assert>
    </rule>
    <rule flag="fatal" context="cbc:InvoicedQuantity[@unitCode] | cbc:BaseQuantity[@unitCode] | cbc:CreditedQuantity[@unitCode]">
      <assert id="ibr-cl-23" flag="fatal" test="((not(contains(normalize-space(@unitCode), ' ')) and contains(' 10 11 13 14 15 20 21 22 23 24 25 27 28 33 34 35 37 38 40 41 56 57 58 59 60 61 74 77 80 81 85 87 89 91 1I 2A 2B 2C 2G 2H 2I 2J 2K 2L 2M 2N 2P 2Q 2R 2U 2X 2Y 2Z 3B 3C 4C 4G 4H 4K 4L 4M 4N 4O 4P 4Q 4R 4T 4U 4W 4X 5A 5B 5E 5J A10 A11 A12 A13 A14 A15 A16 A17 A18 A19 A2 A20 A21 A22 A23 A24 A26 A27 A28 A29 A3 A30 A31 A32 A33 A34 A35 A36 A37 A38 A39 A4 A40 A41 A42 A43 A44 A45 A47 A48 A49 A5 A53 A54 A55 A56 A59 A6 A68 A69 A7 A70 A71 A73 A74 A75 A76 A8 A84 A85 A86 A87 A88 A89 A9 A90 A91 A93 A94 A95 A96 A97 A98 A99 AA AB ACR ACT AD AE AH AI AK AL AMH AMP ANN APZ AQ AS ASM ASU ATM AWG AY AZ B1 B10 B11 B12 B13 B14 B15 B16 B17 B18 B19 B20 B21 B22 B23 B24 B25 B26 B27 B28 B29 B3 B30 B31 B32 B33 B34 B35 B4 B41 B42 B43 B44 B45 B46 B47 B48 B49 B50 B52 B53 B54 B55 B56 B57 B58 B59 B60 B61 B62 B63 B64 B66 B67 B68 B69 B7 B70 B71 B72 B73 B74 B75 B76 B77 B78 B79 B8 B80 B81 B82 B83 B84 B85 B86 B87 B88 B89 B90 B91 B92 B93 B94 B95 B96 B97 B98 B99 BAR BB BFT BHP BIL BLD BLL BP BPM BQL BTU BUA BUI C0 C10 C11 C12 C13 C14 C15 C16 C17 C18 C19 C20 C21 C22 C23 C24 C25 C26 C27 C28 C29 C3 C30 C31 C32 C33 C34 C35 C36 C37 C38 C39 C40 C41 C42 C43 C44 C45 C46 C47 C48 C49 C50 C51 C52 C53 C54 C55 C56 C57 C58 C59 C60 C61 C62 C63 C64 C65 C66 C67 C68 C69 C7 C70 C71 C72 C73 C74 C75 C76 C78 C79 C8 C80 C81 C82 C83 C84 C85 C86 C87 C88 C89 C9 C90 C91 C92 C93 C94 C95 C96 C97 C99 CCT CDL CEL CEN CG CGM CKG CLF CLT CMK CMQ CMT CNP CNT COU CTG CTM CTN CUR CWA CWI D03 D04 D1 D10 D11 D12 D13 D15 D16 D17 D18 D19 D2 D20 D21 D22 D23 D24 D25 D26 D27 D29 D30 D31 D32 D33 D34 D36 D41 D42 D43 D44 D45 D46 D47 D48 D49 D5 D50 D51 D52 D53 D54 D55 D56 D57 D58 D59 D6 D60 D61 D62 D63 D65 D68 D69 D73 D74 D77 D78 D80 D81 D82 D83 D85 D86 D87 D88 D89 D91 D93 D94 D95 DAA DAD DAY DB DBM DBW DD DEC DG DJ DLT DMA DMK DMO DMQ DMT DN DPC DPR DPT DRA DRI DRL DT DTN DWT DZN DZP E01 E07 E08 E09 E10 E12 E14 E15 E16 E17 E18 E19 E20 E21 E22 E23 E25 E27 E28 E30 E31 E32 E33 E34 E35 E36 E37 E38 E39 E4 E40 E41 E42 E43 E44 E45 E46 E47 E48 E49 E50 E51 E52 E53 E54 E55 E56 E57 E58 E59 E60 E61 E62 E63 E64 E65 E66 E67 E68 E69 E70 E71 E72 E73 E74 E75 E76 E77 E78 E79 E80 E81 E82 E83 E84 E85 E86 E87 E88 E89 E90 E91 E92 E93 E94 E95 E96 E97 E98 E99 EA EB EQ F01 F02 F03 F04 F05 F06 F07 F08 F10 F11 F12 F13 F14 F15 F16 F17 F18 F19 F20 F21 F22 F23 F24 F25 F26 F27 F28 F29 F30 F31 F32 F33 F34 F35 F36 F37 F38 F39 F40 F41 F42 F43 F44 F45 F46 F47 F48 F49 F50 F51 F52 F53 F54 F55 F56 F57 F58 F59 F60 F61 F62 F63 F64 F65 F66 F67 F68 F69 F70 F71 F72 F73 F74 F75 F76 F77 F78 F79 F80 F81 F82 F83 F84 F85 F86 F87 F88 F89 F90 F91 F92 F93 F94 F95 F96 F97 F98 F99 FAH FAR FBM FC FF FH FIT FL FNU FOT FP FR FS FTK FTQ G01 G04 G05 G06 G08 G09 G10 G11 G12 G13 G14 G15 G16 G17 G18 G19 G2 G20 G21 G23 G24 G25 G26 G27 G28 G29 G3 G30 G31 G32 G33 G34 G35 G36 G37 G38 G39 G40 G41 G42 G43 G44 G45 G46 G47 G48 G49 G50 G51 G52 G53 G54 G55 G56 G57 G58 G59 G60 G61 G62 G63 G64 G65 G66 G67 G68 G69 G70 G71 G72 G73 G74 G75 G76 G77 G78 G79 G80 G81 G82 G83 G84 G85 G86 G87 G88 G89 G90 G91 G92 G93 G94 G95 G96 G97 G98 G99 GB GBQ GDW GE GF GFI GGR GIA GIC GII GIP GJ GL GLD GLI GLL GM GO GP GQ GRM GRN GRO GV GWH H03 H04 H05 H06 H07 H08 H09 H10 H11 H12 H13 H14 H15 H16 H18 H19 H20 H21 H22 H23 H24 H25 H26 H27 H28 H29 H30 H31 H32 H33 H34 H35 H36 H37 H38 H39 H40 H41 H42 H43 H44 H45 H46 H47 H48 H49 H50 H51 H52 H53 H54 H55 H56 H57 H58 H59 H60 H61 H62 H63 H64 H65 H66 H67 H68 H69 H70 H71 H72 H73 H74 H75 H76 H77 H79 H80 H81 H82 H83 H84 H85 H87 H88 H89 H90 H91 H92 H93 H94 H95 H96 H98 H99 HA HAD HBA HBX HC HDW HEA HGM HH HIU HKM HLT HM HMO HMQ HMT HPA HTZ HUR HWE IA IE INH INK INQ ISD IU IV J10 J12 J13 J14 J15 J16 J17 J18 J19 J2 J20 J21 J22 J23 J24 J25 J26 J27 J28 J29 J30 J31 J32 J33 J34 J35 J36 J38 J39 J40 J41 J42 J43 J44 J45 J46 J47 J48 J49 J50 J51 J52 J53 J54 J55 J56 J57 J58 J59 J60 J61 J62 J63 J64 J65 J66 J67 J68 J69 J70 J71 J72 J73 J74 J75 J76 J78 J79 J81 J82 J83 J84 J85 J87 J90 J91 J92 J93 J95 J96 J97 J98 J99 JE JK JM JNT JOU JPS JWL K1 K10 K11 K12 K13 K14 K15 K16 K17 K18 K19 K2 K20 K21 K22 K23 K26 K27 K28 K3 K30 K31 K32 K33 K34 K35 K36 K37 K38 K39 K40 K41 K42 K43 K45 K46 K47 K48 K49 K50 K51 K52 K53 K54 K55 K58 K59 K6 K60 K61 K62 K63 K64 K65 K66 K67 K68 K69 K70 K71 K73 K74 K75 K76 K77 K78 K79 K80 K81 K82 K83 K84 K85 K86 K87 K88 K89 K90 K91 K92 K93 K94 K95 K96 K97 K98 K99 KA KAT KB KBA KCC KDW KEL KGM KGS KHY KHZ KI KIC KIP KJ KJO KL KLK KLX KMA KMH KMK KMQ KMT KNI KNM KNS KNT KO KPA KPH KPO KPP KR KSD KSH KT KTN KUR KVA KVR KVT KW KWH KWO KWT KWY KX L10 L11 L12 L13 L14 L15 L16 L17 L18 L19 L2 L20 L21 L23 L24 L25 L26 L27 L28 L29 L30 L31 L32 L33 L34 L35 L36 L37 L38 L39 L40 L41 L42 L43 L44 L45 L46 L47 L48 L49 L50 L51 L52 L53 L54 L55 L56 L57 L58 L59 L60 L63 L64 L65 L66 L67 L68 L69 L70 L71 L72 L73 L74 L75 L76 L77 L78 L79 L80 L81 L82 L83 L84 L85 L86 L87 L88 L89 L90 L91 L92 L93 L94 L95 L96 L98 L99 LA LAC LBR LBT LD LEF LF LH LK LM LN LO LP LPA LR LS LTN LTR LUB LUM LUX LY M1 M10 M11 M12 M13 M14 M15 M16 M17 M18 M19 M20 M21 M22 M23 M24 M25 M26 M27 M29 M30 M31 M32 M33 M34 M35 M36 M37 M38 M39 M4 M40 M41 M42 M43 M44 M45 M46 M47 M48 M49 M5 M50 M51 M52 M53 M55 M56 M57 M58 M59 M60 M61 M62 M63 M64 M65 M66 M67 M68 M69 M7 M70 M71 M72 M73 M74 M75 M76 M77 M78 M79 M80 M81 M82 M83 M84 M85 M86 M87 M88 M89 M9 M90 M91 M92 M93 M94 M95 M96 M97 M98 M99 MAH MAL MAM MAR MAW MBE MBF MBR MC MCU MD MGM MHZ MIK MIL MIN MIO MIU MKD MKM MKW MLD MLT MMK MMQ MMT MND MNJ MON MPA MQD MQH MQM MQS MQW MRD MRM MRW MSK MTK MTQ MTR MTS MTZ MVA MWH N1 N10 N11 N12 N13 N14 N15 N16 N17 N18 N19 N20 N21 N22 N23 N24 N25 N26 N27 N28 N29 N3 N30 N31 N32 N33 N34 N35 N36 N37 N38 N39 N40 N41 N42 N43 N44 N45 N46 N47 N48 N49 N50 N51 N52 N53 N54 N55 N56 N57 N58 N59 N60 N61 N62 N63 N64 N65 N66 N67 N68 N69 N70 N71 N72 N73 N74 N75 N76 N77 N78 N79 N80 N81 N82 N83 N84 N85 N86 N87 N88 N89 N90 N91 N92 N93 N94 N95 N96 N97 N98 N99 NA NAR NCL NEW NF NIL NIU NL NM3 NMI NMP NPT NT NTU NU NX OA ODE OHM ON ONZ OPM OT OZA OZI P1 P10 P11 P12 P13 P14 P15 P16 P17 P18 P19 P2 P20 P21 P22 P23 P24 P25 P26 P27 P28 P29 P30 P31 P32 P33 P34 P35 P36 P37 P38 P39 P40 P41 P42 P43 P44 P45 P46 P47 P48 P49 P5 P50 P51 P52 P53 P54 P55 P56 P57 P58 P59 P60 P61 P62 P63 P64 P65 P66 P67 P68 P69 P70 P71 P72 P73 P74 P75 P76 P77 P78 P79 P80 P81 P82 P83 P84 P85 P86 P87 P88 P89 P90 P91 P92 P93 P94 P95 P96 P97 P98 P99 PAL PD PFL PGL PI PLA PO PQ PR PS PTD PTI PTL PTN Q10 Q11 Q12 Q13 Q14 Q15 Q16 Q17 Q18 Q19 Q20 Q21 Q22 Q23 Q24 Q25 Q26 Q27 Q28 Q29 Q3 Q30 Q31 Q32 Q33 Q34 Q35 Q36 Q37 Q38 Q39 Q40 QA QAN QB QR QTD QTI QTL QTR R1 R9 RH RM ROM RP RPM RPS RT S3 S4 SAN SCO SCR SEC SET SG SIE SM3 SMI SQ SQR SR STC STI STK STL STN STW SW SX SYR T0 T3 TAH TAN TI TIC TIP TKM TMS TNE TP TPI TPR TQD TRL TST TTS U1 U2 UB UC VA VLT VP W2 WA WB WCD WE WEB WEE WG WHR WM WSD WTT X1 YDK YDQ YRD Z11 ZP ZZ X43 X44 X1A X1B X1D X1F X1G X1W X2C X3A X3H X4A X4B X4C X4D X4F X4G X4H X5H X5L X5M X6H X6P X7A X7B X8A X8B X8C XAA XAB XAC XAD XAE XAF XAG XAH XAI XAJ XAL XAM XAP XAT XAV XB4 XBA XBB XBC XBD XBE XBF XBG XBH XBI XBJ XBK XBL XBM XBN XBO XBP XBQ XBR XBS XBT XBU XBV XBW XBX XBY XBZ XCA XCB XCC XCD XCE XCF XCG XCH XCI XCJ XCK XCL XCM XCN XCO XCP XCQ XCR XCS XCT XCU XCV XCW XCX XCY XCZ XDA XDB XDC XDG XDH XDI XDJ XDK XDL XDM XDN XDP XDR XDS XDT XDU XDV XDW XDX XDY XEC XED XEE XEF XEG XEH XEI XEN XFB XFC XFD XFE XFI XFL XFO XFP XFR XFT XFW XFX XGB XGI XGL XGR XGU XGY XGZ XHA XHB XHC XHG XHN XHR XIA XIB XIC XID XIE XIF XIG XIH XIK XIL XIN XIZ XJB XJC XJG XJR XJT XJY XKG XKI XLE XLG XLT XLU XLV XLZ XMA XMB XMC XME XMR XMS XMT XMW XMX XNA XNE XNF XNG XNS XNT XNU XNV XO1 XO2 XO3 XO4 XO5 XO6 XO7 XO8 XO9 XOA XOB XOC XOD XOE XOF XOG XOH XOI XOJ XOK XOL XOM XON XOP XOQ XOR XOS XOT XOU XOV XOW XOX XOY XOZ XP1 XP2 XP3 XP4 XPA XPB XPC XPD XPE XPF XPG XPH XPI XPJ XPK XPL XPN XPO XPP XPR XPT XPU XPV XPX XPY XPZ XQA XQB XQC XQD XQF XQG XQH XQJ XQK XQL XQM XQN XQP XQQ XQR XQS XRD XRG XRJ XRK XRL XRO XRT XRZ XSA XSB XSC XSD XSE XSH XSI XSK XSL XSM XSO XSP XSS XST XSU XSV XSW XSX XSY XSZ XT1 XTB XTC XTD XTE XTG XTI XTK XTL XTN XTO XTR XTS XTT XTU XTV XTW XTY XTZ XUC XUN XVA XVG XVI XVK XVL XVN XVO XVP XVQ XVR XVS XVY XWA XWB XWC XWD XWF XWG XWH XWJ XWK XWL XWM XWN XWP XWQ XWR XWS XWT XWU XWV XWW XWX XWY XWZ XXA XXB XXC XXD XXF XXG XXH XXJ XXK XYA XYB XYC XYD XYF XYG XYH XYJ XYK XYL XYM XYN XYP XYQ XYR XYS XYT XYV XYW XYX XYY XYZ XZA XZB XZC XZD XZF XZG XZH XZJ XZK XZL XZM XZN XZP XZQ XZR XZS XZT XZU XZV XZW XZX XZY XZZ Z9 IUG KWN KWS ODG ODK ODM Q41 Q42 ', concat(' ', normalize-space(@unitCode), ' '))))">[ibr-cl-23]-Unit code (ibt-130), (ibt-150) MUST be coded according to the UN/ECE Recommendation 20 with Rec 21 extension</assert>
    </rule>
    <rule flag="fatal" context="cbc:EmbeddedDocumentBinaryObject[@mimeCode]">
      <assert id="ibr-cl-24" flag="fatal" test="((@mimeCode = 'application/pdf' or @mimeCode = 'image/png' or @mimeCode = 'image/jpeg' or @mimeCode = 'text/csv' or @mimeCode = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' or @mimeCode = 'application/xml' or @mimeCode = 'application/vnd.oasis.opendocument.spreadsheet'))">[ibr-cl-24]-Mime code (ibt-125-1) MUST be according to subset of IANA code list.</assert>
    </rule>
    <rule flag="fatal" context="cbc:EndpointID[@schemeID]">
      <assert id="ibr-cl-25" flag="fatal" test="((not(contains(normalize-space(@schemeID), ' ')) and contains(' 0002 0007 0009 0037 0060 0088 0096 0097 0106 0130 0135 0142 0151 0183 0184 0188 0190 0191 0192 0193 0195 0196 0198 0199 0200 0201 0202 0204 0208 0209 0210 0211 0212 0213 0215 0216 0218 0221 0230 9901 9910 9913 9914 9915 9918 9919 9920 9922 9923 9924 9925 9926 9927 9928 9929 9930 9931 9932 9933 9934 9935 9936 9937 9938 9939 9940 9941 9942 9943 9944 9945 9946 9947 9948 9949 9950 9951 9952 9953 9957 9959 ', concat(' ', normalize-space(@schemeID), ' '))))">[ibr-cl-25]-Endpoint identifier scheme identifier (ibt-034-1), (ibt-049-1) MUST belong to the CEF EAS code list</assert>
    </rule>
    <rule flag="fatal" context="cac:DeliveryLocation/cbc:ID[@schemeID]">
      <assert id="ibr-cl-26" flag="fatal" test="((not(contains(normalize-space(@schemeID), ' ')) and contains(' 0002 0003 0004 0005 0006 0007 0008 0009 0010 0011 0012 0013 0014 0015 0016 0017 0018 0019 0020 0021 0022 0023 0024 0025 0026 0027 0028 0029 0030 0031 0032 0033 0034 0035 0036 0037 0038 0039 0040 0041 0042 0043 0044 0045 0046 0047 0048 0049 0050 0051 0052 0053 0054 0055 0056 0057 0058 0059 0060 0061 0062 0063 0064 0065 0066 0067 0068 0069 0070 0071 0072 0073 0074 0075 0076 0077 0078 0079 0080 0081 0082 0083 0084 0085 0086 0087 0088 0089 0090 0091 0093 0094 0095 0096 0097 0098 0099 0100 0101 0102 0104 0105 0106 0107 0108 0109 0110 0111 0112 0113 0114 0115 0116 0117 0118 0119 0120 0121 0122 0123 0124 0125 0126 0127 0128 0129 0130 0131 0132 0133 0134 0135 0136 0137 0138 0139 0140 0141 0142 0143 0144 0145 0146 0147 0148 0149 0150 0151 0152 0153 0154 0155 0156 0157 0158 0159 0160 0161 0162 0163 0164 0165 0166 0167 0168 0169 0170 0171 0172 0173 0174 0175 0176 0177 0178 0179 0180 0183 0184 0185 0186 0187 0188 0189 0190 0191 0192 0193 0194 0195 0196 0197 0198 0199 0200 0201 0202 0203 0204 0205 0206 0207 0208 0209 0210 0211 0212 0213 0214 0215 0216 0217 0218 0219 0220 0221 0222 0223 0224 0225 0226 0227 0228 0229 0230 ', concat(' ', normalize-space(@schemeID), ' '))))">[ibr-cl-26]-Delivery location identifier scheme identifier (ibt-071-1) MUST belong to the ISO 6523 ICD code list</assert>
    </rule>
  </pattern>
</schema>