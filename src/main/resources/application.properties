spring.application.name=Peppol Validation
server.servlet.context-path=/peppol-validation-service

# Default properties
spring.profiles.active=prod

#spring.mvc.servlet.path=/peppol-validation

logging.level.root=info
logging.level.org.springframework.web=debug

spring.config.additional-location=classpath:/messages/messages_errorcode.properties
spring.messages.basename=messages/messages_errorcode, messages/messages_validation

auth.token.header=api-key
auth.token.apikey=qwerty

enable.swagger = true
api.key.auth.enabled=true

spring.web.resources.static-locations=classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/

springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html

# Max file size for large tests
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB