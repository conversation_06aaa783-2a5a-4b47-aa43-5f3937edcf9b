package com.perennialsys.peppol.validation.exception.exceptions;

public class RecordNotFoundException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private Object[] args;

    public Object[] getArgs() {
        return args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    public RecordNotFoundException(Object[] args) {
        super();
        this.args = args;
    }

    public RecordNotFoundException(String message) {
        super(message);

    }
}
