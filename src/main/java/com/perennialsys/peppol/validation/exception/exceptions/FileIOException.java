package com.perennialsys.peppol.validation.exception.exceptions;

public class FileIOException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private Object[] args;

    public Object[] getArgs() {
        return args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    public FileIOException(Object[] args) {
        super();
        this.args = args;
    }

    public FileIOException(String message) {
        super(message);

    }
}
