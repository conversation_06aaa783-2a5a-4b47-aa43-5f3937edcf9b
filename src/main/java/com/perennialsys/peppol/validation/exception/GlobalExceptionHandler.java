package com.perennialsys.peppol.validation.exception;

import com.perennialsys.peppol.validation.exception.constants.ExceptionConstantsMap;
import com.perennialsys.peppol.validation.exception.dto.ErrorResponse;
import com.perennialsys.peppol.validation.exception.exceptions.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.Calendar;

/**
 * GlobalExceptionHandler is responsible for handling exceptions globally across the application.
 * It intercepts exceptions thrown by controllers and returns appropriate error responses.
 */

@Slf4j
@ControllerAdvice
@PropertySource("classpath:errormap.properties")
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    @Autowired
    Environment env; // Injects Environment to access properties from errormap.properties

    @Autowired
    MessageSource messageSource; // Injects MessageSource to get localized error messages


    /**
     * Handles RecordNotFoundException and returns an appropriate error response.
     *
     * @param ex The RecordNotFoundException object.
     * @param request The WebRequest object.
     * @return A ResponseEntity with ErrorResponse and HttpStatus.INTERNAL_SERVER_ERROR.
     */

    @ExceptionHandler(Exception.class)
    public final ResponseEntity<ErrorResponse> handleGenericException(Exception ex, WebRequest request) {
        try{
            log.info("START :: CLASS :: GlobalExceptionHandler :: METHOD :: handleGenericException");
            String errorCode = env.getProperty(ExceptionConstantsMap.GENERIC_EXCEPTION, "peppol.validation.GenericException");
            String errorMessage = messageSource.getMessage(errorCode, null, LocaleContextHolder.getLocale());
            ErrorResponse errorResponse = new ErrorResponse(errorCode, Calendar.getInstance().getTime(), "", errorMessage,
                    ex.getLocalizedMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }finally {
            log.info("END :: CLASS :: GlobalExceptionHandler :: METHOD :: handleGenericException");
        }
    }

    @ExceptionHandler(RuntimeException.class)
    public final ResponseEntity<ErrorResponse> handleRuntimeException(RuntimeException ex, WebRequest request) {
        try{
            log.info("START :: CLASS :: GlobalExceptionHandler :: METHOD :: handleRuntimeException");
            String errorCode = env.getProperty(ExceptionConstantsMap.GENERIC_EXCEPTION, "peppol.validation.GenericException");
            String errorMessage = messageSource.getMessage(errorCode, null, LocaleContextHolder.getLocale());
            ErrorResponse errorResponse = new ErrorResponse(errorCode, Calendar.getInstance().getTime(), "", errorMessage,
                    ex.getLocalizedMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }finally {
            log.info("END :: CLASS :: GlobalExceptionHandler :: METHOD :: handleRuntimeException");
        }
    }

    @ExceptionHandler(ErrorListMappingException.class)
    public final ResponseEntity<ErrorResponse> handleErrorListMappingException(ErrorListMappingException ex, WebRequest request) {
        try{
            log.info("START :: CLASS :: GlobalExceptionHandler :: METHOD :: handleErrorListMappingException");
            String errorCode = env.getProperty(ExceptionConstantsMap.ERROR_LIST_MAPPING_EXCEPTION, "peppol.validation.GenericException");
            String errorMessage = messageSource.getMessage(errorCode, null, LocaleContextHolder.getLocale());
            ErrorResponse errorResponse = new ErrorResponse(errorCode, Calendar.getInstance().getTime(), "", errorMessage,
                    ex.getLocalizedMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }finally {
            log.info("END :: CLASS :: GlobalExceptionHandler :: METHOD :: handleErrorListMappingException");
        }
    }

    @ExceptionHandler(FileIOException.class)
    public final ResponseEntity<ErrorResponse> handleErrorListMappingException(FileIOException ex, WebRequest request) {
        try{
            log.info("START :: CLASS :: GlobalExceptionHandler :: METHOD :: handleFileIOException");
            String errorCode = env.getProperty(ExceptionConstantsMap.FILE_IO_EXCEPTION, "peppol.validation.GenericException");
            String errorMessage = messageSource.getMessage(errorCode, null, LocaleContextHolder.getLocale());
            ErrorResponse errorResponse = new ErrorResponse(errorCode, Calendar.getInstance().getTime(), "", errorMessage,
                    ex.getLocalizedMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }finally {
            log.info("END :: CLASS :: GlobalExceptionHandler :: METHOD :: handleFileIOException");
        }
    }


    @ExceptionHandler(ValidationExecutionException.class)
    public final ResponseEntity<ErrorResponse> handleValidationExecutionException(ValidationExecutionException ex, WebRequest request) {
        try{
            log.info("START :: CLASS :: GlobalExceptionHandler :: METHOD :: handleValidationExecutionException");
            String errorCode = env.getProperty(ExceptionConstantsMap.VALIDATION_EXECUTION_EXCEPTION, "peppol.validation.GenericException");
            String errorMessage = messageSource.getMessage(errorCode, null, LocaleContextHolder.getLocale());
            ErrorResponse errorResponse = new ErrorResponse(errorCode, Calendar.getInstance().getTime(), "", errorMessage,
                    ex.getLocalizedMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }finally {
            log.info("END :: CLASS :: GlobalExceptionHandler :: METHOD :: handleValidationExecutionException");
        }
    }

    @ExceptionHandler(RecordNotFoundException.class)
    public final ResponseEntity<ErrorResponse> handleRecordNotFoundException(RecordNotFoundException ex, WebRequest request) {
        try{
            log.info("START :: CLASS :: GlobalExceptionHandler :: METHOD :: handleRecordNotFoundException");
            String errorCode = env.getProperty(ExceptionConstantsMap.RECORD_NOT_FOUND_EXCEPTION, "peppol.validation.GenericException");
            String errorMessage = messageSource.getMessage(errorCode, null, LocaleContextHolder.getLocale());
            ErrorResponse errorResponse = new ErrorResponse(errorCode, Calendar.getInstance().getTime(), "", errorMessage,
                    ex.getLocalizedMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }finally {
            log.info("END :: CLASS :: GlobalExceptionHandler :: METHOD :: handleRecordNotFoundException ");
        }
    }
}
