package com.perennialsys.peppol.validation.exception.constants;

/**
 * This class serves as a central location for defining constant keys for various exception types
 * used across the application. These constants are mapped to specific error codes or messages
 * that can be referenced in different parts of the application, such as in exception handlers
 * or when generating error responses.
 */
public class ExceptionConstantsMap {

    public static final String GENERIC_EXCEPTION = "peppol.validation.GenericException";
    public static final String ERROR_LIST_MAPPING_EXCEPTION = "peppol.validation.ErrorListMappingException";
    public static final String FILE_IO_EXCEPTION = "peppol.validation.FileIOException";
    public static final String VALIDATION_EXECUTION_EXCEPTION = "peppol.validation.ValidationExecutionException";
    public static final String RECORD_NOT_FOUND_EXCEPTION = "peppol.validation.RecordNotFoundException";
    public static final String BAD_CREDENTIAL_EXCEPTION = "peppol.validation.BadCredentialsException";
  }
