package com.perennialsys.peppol.validation.exception.exceptions;

public class TemplateException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private Object[] args;

    public Object[] getArgs() {
        return args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    public TemplateException(Object[] args) {
        super();
        this.args = args;
    }

    public TemplateException(String message) {
        super(message);

    }
}
