package com.perennialsys.peppol.validation.enums;

public enum PintSpecifications {

    PINT_MY("PintMy","org.peppol.pint.my"),
    PINT_AUNZ("PintAUNZ","org.peppol.pint.aunz"),
    REPORTING("ReportingEUSR","eu.peppol.reporting"),
    PEPPOL_BIS_3("PeppolBis3","eu.peppol.bis3"),
    PEPPOL_SG_BIS_3("PeppolSgBis3","eu.peppol.bis3.sg.ubl"),
    PEPPOL_SG_PINT("PeppolSgPint3", "org.peppol.pint.sg"),
    PEPPOL_AE_PINT("PintAE", "org.peppol.pint.ae");


    private final String code;
    private final String groupId;


    PintSpecifications(String code, String groupId) {
        this.code = code;
        this.groupId = groupId;
    }

    // Getter for groupId
    public String getGroupId() {
        return this.groupId;
    }
    // Static method to get groupId by code
    public static String getGroupIdByPintSpec(String code) {
        for (PintSpecifications mapper : PintSpecifications.values()) {
            if (mapper.code.equals(code)) {
                return mapper.getGroupId();
            }
        }
        throw new RuntimeException("No Groupid found for " + code);
    }
}
