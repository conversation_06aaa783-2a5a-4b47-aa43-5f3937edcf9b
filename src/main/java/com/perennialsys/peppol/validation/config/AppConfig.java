package com.perennialsys.peppol.validation.config;

import com.perennialsys.peppol.validation.config.interceptor.AuthenticateInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class AppConfig implements WebMvcConfigurer {

    @Autowired
    AuthenticateInterceptor interceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // Register the interceptor and specify the path patterns to intercept
        registry.addInterceptor(interceptor).excludePathPatterns("/");
    }

    @Bean(name = "multipartResolver")
    public StandardServletMultipartResolver multipartResolver(){
        return new StandardServletMultipartResolver();
    }
}
