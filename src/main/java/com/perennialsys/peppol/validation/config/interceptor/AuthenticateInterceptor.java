package com.perennialsys.peppol.validation.config.interceptor;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Iterator;

/**
 * Intercepts HTTP requests to validate the content type of uploaded files.
 * <p>
 * This interceptor is specifically designed to handle multipart file uploads and ensure
 * that the uploaded files are of the correct XML type. If the file content type is not
 * XML, the request will be rejected with an appropriate error message.
 * </p>
 * <p>
 * The interceptor logs the start and end of each request handling, along with details
 * about the files being processed.
 * </p>
 */

@Slf4j
@Component
public class AuthenticateInterceptor implements HandlerInterceptor {

    /**
     * Intercepts and processes HTTP requests before they reach the controller.
     * <p>
     * This method checks whether the incoming request is a multipart request and validates the content type
     * of the uploaded files. If the content type is not XML, the request processing is stopped, and an error
     * response is sent to the client. Logs are generated at the start and end of the method, as well as for
     * important details like the file names and content types.
     * </p>
     *
     * @param request  the current HTTP request
     * @param response the current HTTP response
     * @param handler  chosen handler to execute, for type and/or instance evaluation
     * @return {@code true} if the execution chain should proceed with the next interceptor or the handler itself,
     * {@code false} to indicate that request processing should stop
     * @throws Exception in case of errors during request processing
     */

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            log.info("START :: CLASS :: AuthenticateInterceptor :: METHOD :: preHandle :: REQUEST_URI :: {}", request.getRequestURI());

            String contentType = request.getContentType();

            if (request instanceof MultipartHttpServletRequest) {
                MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
                Iterator<String> fileNames = multipartRequest.getFileNames();

                while (fileNames.hasNext()) {
                    MultipartFile file = multipartRequest.getFile(fileNames.next());
                    if (file != null) {
                        String fileContentType = file.getContentType();
                        log.info("FILE_NAME :: {} :: CONTENT_TYPE :: {}", file.getOriginalFilename(), fileContentType);
                        if (fileContentType == null) {
                            response.sendError(HttpServletResponse.SC_NOT_FOUND, "File Not Found");
                            return false;  // Stop further processing
                        }
                        // Check if the file's content type is XML
                        if (!"application/xml".equals(fileContentType) && !"text/xml".equals(fileContentType)) {
                            response.sendError(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE, "Invalid file type. Only XML files are accepted.");
                            return false;  // Stop further processing
                        }
                    }
                }
            }
            // Continue with the request if everything is valid
            return true;
        }finally {
            log.info("END :: CLASS :: ValidationInterceptor :: METHOD :: preHandle");
        }
    }

}
