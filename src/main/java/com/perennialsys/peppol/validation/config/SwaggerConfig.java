package com.perennialsys.peppol.validation.config;

import io.swagger.v3.oas.models.OpenAPI;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
@PropertySource("classpath:application.properties")
public class SwaggerConfig {

    @Value("${enable.swagger}")
    private boolean enableSwagger;

    @Bean
    OpenAPI customOpenAPI() {
        return new OpenAPI();
    }
}
