package com.perennialsys.peppol.validation.constants;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;


/**
 * Utility class that contains predefined validation rules and patterns for XML validation.
 * <p>
 * This class provides a set of static regular expression patterns that are used to validate
 * various components of an XML document, such as header versions and timestamps.
 * </p>
 * <p>
 * These patterns can be used to ensure compliance with specific XML formats and standards.
 * </p>
 *
 *   <AUTHOR>
 *   @since 1.0
 *   @version 1.0
 */
public class ValidationRulesAndPatterns {
    /**
     * <PERSON><PERSON> for validating the version of the XML header. Expected version is "1.0".
     */
    public static final Pattern HEADER_VERSION_PATTERN = Pattern.compile("1.0");
    // private static final Pattern IDENTIFIER_PATTERN = Pattern.compile("\\d{4}:");

    /**
     * Pattern for validating timestamps in the format "yyyy-MM-dd'T'HH:mm:ss'Z'".
     * <p>
     * Example: 2023-09-24T12:34:56Z
     * </p>
     */
    public static final Pattern TIMESTAMP_PATTERN = Pattern.compile("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z");


    // Allowed elements under the <Sender> element
    public static final Set<String> SENDER_ALLOWED_ELEMENTS = new HashSet<>(Arrays.asList(
            "Identifier", "Authority", "ContactInformation"
    ));

    // Allowed sub-elements under the <ContactInformation> element
    public static final Set<String> CONTACT_INFO_ELEMENTS = new HashSet<>( Arrays.asList(
            "Contact", "EmailAddress", "TelephoneNumber", "FaxNumber", "ContactTypeIdentifier"
    ));
}
