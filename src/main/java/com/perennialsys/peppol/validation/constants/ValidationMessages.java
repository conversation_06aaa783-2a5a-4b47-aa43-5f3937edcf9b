package com.perennialsys.peppol.validation.constants;

/**
 * Utility class that contains predefined validation messages for various validation errors.
 * <p>
 * This class provides a set of static string constants used as error messages when validating
 * XML documents and related fields. These messages ensure that the necessary elements like
 * document type, version, and XML string are present and valid.
 * </p>
 *
 *   <AUTHOR>
 *   @since 1.0
 *   @version 1.0
 */
public class ValidationMessages {

    /**
     * Error message indicating that the document must not be null or empty.
     */
    public static final String DOCUMENT_NOT_NULL_OR_EMPTY = "Document must not be null or empty";

    /**
     * Error message indicating that the PINT specification must not be null or empty.
     */
    public static final String PINT_SPEC_NOT_NULL_OR_EMPTY = "Pint specification must not be null or empty";

    /**
     * Error message indicating that the document type must not be null or empty.
     */
    public static final String DOCUMENT_TYPE_NOT_NULL_OR_EMPTY = "Document type must not be null or empty";

    /**
     * Error message indicating that the version must not be null or empty.
     */
    public static final String VERSION_NOT_NULL_OR_EMPTY = "Version must not be null or empty";

    /**
     * Error message indicating that the XML string must not be null or empty.
     */
    public static final String XML_NOT_NULL_OR_EMPTY = "XML String must not be null or empty";

}
