package com.perennialsys.peppol.validation.controller;

import com.helger.schematron.sch.SchematronResourceSCH;
import com.helger.schematron.svrl.SVRLFailedAssert;
import com.helger.schematron.svrl.SVRLHelper;
import com.perennialsys.peppol.validation.constants.APIParamsAndMessage;
import com.perennialsys.peppol.validation.constants.ApiUrls;
import com.perennialsys.peppol.validation.constants.ValidationMessages;
import com.perennialsys.peppol.validation.dto.ValidationSummary;
import com.perennialsys.peppol.validation.handler.IValidatorHandler;
import com.perennialsys.peppol.validation.handler.impl.ValidatorHandlerImpl;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.xml.transform.stream.StreamSource;
import java.io.File;
import java.io.IOException;


/**
 * Controller responsible for validating XML files against specified Schematron rules.
 * <p>
 * This controller provides endpoints to validate XML documents based on
 * various specifications like PINT, document type, and version. The validation
 * results include detailed error lists if the validation fails.
 * </p>
 *
 * <p>
 * The main functionality is provided by the {@link IValidatorHandler} service, which
 * processes the XML files and performs the actual validation.
 * </p>
 *
 * <p>
 * All endpoints are exposed under the base URL defined in {@link ApiUrls#REQUEST_MAPPING}.
 * </p>
 *
 *   <AUTHOR> Narayane
 *   @since 1.0
 *   @version 1.0
 */


@Slf4j
@RestController
@RequestMapping(ApiUrls.REQUEST_MAPPING)
public class ValidationController {

    @Autowired
    IValidatorHandler iValidatorHandler;
    @Autowired
    private ValidatorHandlerImpl validatorHandlerImpl;


    /**
     * Validates the provided XML file against the specified Schematron rules.
     * <p>
     * This endpoint takes an XML file as input along with specifications for validation,
     * such as the PINT specification, document type, and version. The file is validated
     * against the corresponding Schematron mapped to the specifications. If validation
     * fails, the method will return a detailed list of errors.
     * </p>
     *
     * @param file      The XML file to be validated. This parameter is mandatory.
     *                  It should be passed as a multipart file in the request.
     * @param pintSpec  The specification used for validation (e.g., "PINT").
     *                  This value should be passed in the "pint-spec" header.
     * @param docType   The document type ID value for the validation.
     *                  This value should be passed in the "doc-type" header.
     * @param version   The version of the document type to be validated.
     *                  This value should be passed in the "version" header.
     * @return          A {@link ResponseEntity} containing a {@link ValidationSummary} object.
     *                  The response will include validation errors if any are found.
     *                  The HTTP status will be 200 OK if the request is successful.
     * @throws IOException If the file cannot be read or is of an invalid type.
     *   <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */

    @Operation(summary = "This Controller Should Be Used to Validate the Document")
    @PostMapping(value = ApiUrls.IS_VALID,
                consumes =  MediaType.MULTIPART_FORM_DATA_VALUE,
                produces =  MediaType.APPLICATION_JSON_VALUE )
    public ResponseEntity<ValidationSummary> validateDocument(
            @NotNull(message = ValidationMessages.DOCUMENT_NOT_NULL_OR_EMPTY)
            @RequestPart("file") MultipartFile file,
            @RequestHeader(APIParamsAndMessage.PINT_SPECIFICATION)
                @NotBlank(message = ValidationMessages.PINT_SPEC_NOT_NULL_OR_EMPTY)
                String pintSpec,
            @RequestHeader(APIParamsAndMessage.DOCUMENT_TYPE)
                @NotBlank(message = ValidationMessages.DOCUMENT_TYPE_NOT_NULL_OR_EMPTY)
                String docType,
            @RequestHeader(APIParamsAndMessage.DOCUMENT_VERSION)
              @NotBlank(message = ValidationMessages.VERSION_NOT_NULL_OR_EMPTY)
            String version) throws IOException {

            log.info("START :: CLASS :: ValidationController :: METHOD :: validateDocument  :: FILE_NAME :: {} :: PINT_SPEC :: {} :: ARTEFACT_ID "+
                    ":: {} :: VERSION :: {} ::", file.getOriginalFilename(), pintSpec, docType, version);

            ValidationSummary validationSummary = iValidatorHandler.validateXMLSource(file, pintSpec, docType, version);

            log.info("END :: CLASS :: ValidationController :: METHOD :: validateDocument  :: FILE_NAME :: {} :: PINT_SPEC :: {} :: ARTEFACT_ID "+
                        ":: {} :: VERSION :: {} ::", file.getOriginalFilename(), pintSpec, docType, version);
            return new ResponseEntity<>(validationSummary, HttpStatus.OK);


    }


    /**
     * This controller endpoint is used to validate the XML String document.
     *
     * <p>It accepts the XML document as a string in the request body, along with headers specifying the
     * PINT specification, document type, and document version. The method returns a validation summary
     * that contains the results of the validation process.</p>
     *
     * @param xmlString the XML document to be validated, passed in the request body.
     *                  Must not be null and must contain valid XML content.
     * @param pintSpec  the PINT specification version to validate the XML document against.
     *                  Retrieved from the request header {@code APIParamsAndMessage.PINT_SPECIFICATION}.
     *                  Must not be blank or null.
     * @param docType   the document type to be validated.
     *                  Retrieved from the request header {@code APIParamsAndMessage.DOCUMENT_TYPE}.
     *                  Must not be blank or null.
     * @param version   the version of the document to be validated.
     *                  Retrieved from the request header {@code APIParamsAndMessage.DOCUMENT_VERSION}.
     *                  Must not be blank or null.
     * @return a {@link ResponseEntity} containing the {@link ValidationSummary} that holds the validation
     *         results. The response entity has a status of {@code HttpStatus.OK} if validation is successful.
     * @throws IOException if an I/O error occurs during the validation process.
     *
     * @see ValidationSummary
     * @see ResponseEntity
     * @see HttpStatus
     *
     *   <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */
    @Operation(summary = "This Controller Should Be Used to Validate the XML String Document")
    @PostMapping(value = ApiUrls.IS_VALID_XML_STRING,
            produces =  MediaType.APPLICATION_JSON_VALUE )
    public ResponseEntity<ValidationSummary> validateXMLStringDocument(
            @NotNull(message = ValidationMessages.DOCUMENT_NOT_NULL_OR_EMPTY)
            @RequestBody String xmlString,
            @RequestHeader(APIParamsAndMessage.PINT_SPECIFICATION)
            @NotBlank(message = ValidationMessages.PINT_SPEC_NOT_NULL_OR_EMPTY)
            String pintSpec,
            @RequestHeader(APIParamsAndMessage.DOCUMENT_TYPE)
            @NotBlank(message = ValidationMessages.DOCUMENT_TYPE_NOT_NULL_OR_EMPTY)
            String docType,
            @RequestHeader(APIParamsAndMessage.DOCUMENT_VERSION)
            @NotBlank(message = ValidationMessages.VERSION_NOT_NULL_OR_EMPTY)
            String version) throws IOException {

            log.info("START :: CLASS :: ValidationController :: METHOD :: validateXMLStringDocument  :: PINT_SPEC :: {} :: ARTEFACT_ID "+
                    ":: {} :: VERSION :: {} ::", pintSpec, docType, version);

            ValidationSummary validationSummary = iValidatorHandler.validateXMLSource(xmlString, pintSpec, docType, version);

            log.info("END :: CLASS :: ValidationController :: METHOD :: validateXMLStringDocument  :: PINT_SPEC :: {} :: ARTEFACT_ID "+
                        ":: {} :: VERSION :: {} ::", pintSpec, docType, version);

            return new ResponseEntity<>(validationSummary, HttpStatus.OK);

    }



    /**This API is used for validating SBDH.

        It is under developement. It will accept XML file

        which contains Header and Payload and Schematron file

     *   <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */
    @PostMapping(value = "/sbdh")
    public ResponseEntity<String> validateSBDHDocument(
            @RequestPart("xmlFile") MultipartFile xml,
            @RequestPart("schamatron") MultipartFile sch) throws Exception {
        log.info("START :: CLASS :: ValidationController :: METHOD :: validateSBDHDocument :: ");

        //File xmlFile = ValidatorHandlerImpl.convertMultipartFileToFile(xml);
        //File schFile = ValidatorHandlerImpl.convertMultipartFileToFile(sch);

        SchematronResourceSCH schResource = SchematronResourceSCH.fromInputStream("", sch.getInputStream());

        if (!schResource.isValidSchematron()) {
            log.error("Schematron is not valid.");
            return new ResponseEntity<>("Schematron is not valid.", HttpStatus.BAD_REQUEST);
        }

        // Perform validation
        StreamSource xmlSource = new StreamSource(xml.getInputStream());
        var validationResults = schResource.applySchematronValidationToSVRL(xmlSource);

        log.info("ValidationResult : {}", validationResults.toString());
        // Check for failed assertions
        for (SVRLFailedAssert failedAssert : SVRLHelper.getAllFailedAssertions(validationResults)) {
            System.out.println("Error: " + failedAssert.getText());
        }

        log.info("END :: CLASS :: ValidationController :: METHOD :: validateSBDHDocument :: ");
        return new ResponseEntity<>(validationResults.toString(), HttpStatus.OK);

    }

}
