package com.perennialsys.peppol.validation.handler.impl;


import com.helger.commons.error.list.ErrorList;
import com.helger.commons.io.resource.ClassPathResource;
import com.helger.diver.api.coord.DVRCoordinate;
import com.helger.phive.api.execute.ValidationExecutionManager;
import com.helger.phive.api.executorset.IValidationExecutorSet;
import com.helger.phive.api.executorset.ValidationExecutorSetRegistry;
import com.helger.phive.api.result.ValidationResultList;
import com.helger.phive.api.validity.IValidityDeterminator;
import com.helger.phive.xml.source.IValidationSourceXML;
import com.helger.phive.xml.source.ValidationSourceXML;
import com.perennialsys.peppol.validation.constants.ValidationMessages;
import com.perennialsys.peppol.validation.constants.ValidationRulesAndPatterns;
import com.perennialsys.peppol.validation.dto.CustomMultipartFile;
import com.perennialsys.peppol.validation.dto.ValidationArtefact;
import com.perennialsys.peppol.validation.dto.ValidationDetails;
import com.perennialsys.peppol.validation.dto.ValidationErrorList;
import com.perennialsys.peppol.validation.dto.ValidationSummary;
import com.perennialsys.peppol.validation.enums.ElementTagNames;
import com.perennialsys.peppol.validation.enums.PintSpecifications;
import com.perennialsys.peppol.validation.exception.exceptions.ErrorListMappingException;
import com.perennialsys.peppol.validation.exception.exceptions.ValidationExecutionException;
import com.perennialsys.peppol.validation.handler.IValidatorHandler;
import com.perennialsys.peppol.validation.mock.CTestFiles;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;

/**
 * Service implementation for handling XML validation against Schematron rules.
 * <p>
 * This class implements the {@link IValidatorHandler} interface and provides the logic
 * to validate XML files against specific Schematron rules based on the provided PINT specification,
 * document type, and version. It processes the uploaded file and returns a summary of validation results.
 * </p>
 *   <AUTHOR> Narayane
 *   @since 1.0
 *   @version 1.0

 */


@Slf4j
@Service
public class ValidatorHandlerImpl implements IValidatorHandler {



    /**
     * Validates the provided XML file against the specified Schematron rules.
     * <p>
     * This method takes an XML file as input along with the PINT specification, document type,
     * and version. It performs validation based on the mapped Schematron rules and returns a summary
     * of validation errors, if any. The method ensures that all input parameters are non-null and
     * non-empty, enforcing constraints through annotations.
     * </p>
     *
     * @param multipartFile      The XML file to be validated. This file is passed as a {@link MultipartFile}.
     *                  It should not be null and must be a valid XML file.
     * @param pintSpec  The PINT specification used for validation. This value is required and cannot be null or empty.
     *                  A validation message is provided through the {@code pint.spec.notnullorempty} property if this field is invalid.
     * @param docType   The document type used for validation. This value is required and cannot be null or empty.
     *                  A validation message is provided through the {@code document.type.notnullorempty} property if this field is invalid.
     * @param versionNo The version of the document type. This value is required and cannot be null or empty.
     *                  A validation message is provided through the {@code version.notnullorempty} property if this field is invalid.
     * @return          A {@link ValidationSummary} object that contains the results of the validation, including any errors found.
     * @throws ErrorListMappingException If there are any issues during the validation process, this exception will be thrown.

     *   <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */

    @Override
    public ValidationSummary validateXMLSource(
            MultipartFile multipartFile,
            @NotBlank(message = ValidationMessages.PINT_SPEC_NOT_NULL_OR_EMPTY) String pintSpec,
            @NotBlank(message = ValidationMessages.DOCUMENT_TYPE_NOT_NULL_OR_EMPTY) String docType,
            @NotBlank(message = ValidationMessages.VERSION_NOT_NULL_OR_EMPTY) String versionNo) {

            try {

                log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource :: FILE_NAME :: {} :: PINT_SPEC :: {} :: ARTEFACT_ID " +
                        ":: {} :: VERSION :: {} ::", multipartFile.getOriginalFilename(), pintSpec, docType, versionNo);


                String groupId = PintSpecifications.getGroupIdByPintSpec(pintSpec);

                DVRCoordinate aVESID = DVRCoordinate.create(groupId, docType, versionNo);

                //final ValidationExecutorSetRegistry <IValidationSourceXML> aVESRegistry = new ValidationExecutorSetRegistry<> ();

                IValidationExecutorSet<IValidationSourceXML> aExecutors = CTestFiles.VES_REGISTRY.getOfID (aVESID);

                Document xmlDocument = convertMultipartFileToDocument(multipartFile);

                IValidationSourceXML aSource;

                if (isSBDH(xmlDocument)){

                    Node businessDocument = extractBusinessDocumentFromSBDH(xmlDocument);

                    aSource = ValidationSourceXML.create("", businessDocument); //TODO : Identify the first parameter purpose and its value

                    validateSBDHsource(xmlDocument);         //TODO : This will validate the SBDH Source, Work In Progress

                }else{
                    Node sbdNode = xmlDocument.getDocumentElement();
                    aSource = ValidationSourceXML.create("", sbdNode);
                }

                log.info(" CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource :: VALIDATION_DOCUMENT_SOURCE :: {}", aSource);
                log.info(" CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource :: VALIDATION_DOCUMENT_EXECUTORS :: {}", aExecutors);

                ValidationResultList aErrors;

                try {
                    assert aExecutors != null;
                    aErrors = ValidationExecutionManager.executeValidation(IValidityDeterminator.createDefault(),
                            aExecutors,
                            aSource,
                            Locale.US);

                } catch (Exception e) {
                    log.error("EXCEPTION :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource :: REASON :: {}", e.getMessage());
                    throw new ValidationExecutionException("Failed to validate the document : " + e.getMessage());
                }

                List<ValidationArtefact> validationArtefactList = new ArrayList<>();
                List<ValidationDetails> validationDetailsList = new ArrayList<>();

                ValidationSummary validationSummary = new ValidationSummary();

                try {
                    aErrors.forEach(a -> {
                        ValidationArtefact validationArtefact = new ValidationArtefact();
                        List<ValidationErrorList> errorLists = new ArrayList<>();
                        validationArtefact.setIsSuccess(!a.getErrorList().isEmpty());
                        validationArtefact.setType(String.valueOf(a.getValidationArtefact().getValidationType()));
                        validationArtefact.setResourceCPath(a.getValidationArtefact().getRuleResource().getPath());
                        validationArtefact.setResourceClassLoader(a.getValidationArtefact().getRuleResource().getClass().toString());
                        validationArtefact.setResourceURL(Objects.requireNonNull(a.getValidationArtefact().getRuleResource().getAsURL()).toString());
                        a.getErrorList().getAllErrors().forEach(e -> {
                            ValidationErrorList errors = new ValidationErrorList();
                            errors.setErrorLevel(e.getErrorLevel().toString());
                            errors.setErrorId(e.getErrorID());
                            errors.setErrorFieldName(e.getErrorFieldName());
                            errors.setErrorLocationResourceID(e.getErrorLocation().getResourceID());
                            errors.setErrorText(Objects.requireNonNull(e.getErrorTexts()).getDisplayText(Locale.US));
                            errors.setXPathText(e.getAsString(Locale.US));
                            errorLists.add(errors);
                        });
                        validationArtefact.setErrorList(errorLists);
                        validationArtefactList.add(validationArtefact);

                        ValidationDetails validationDetails = new ValidationDetails();
                        validationDetails.setValidationType(validationArtefact.getType());
                        validationDetails.setValidationArtefact(validationArtefact.getResourceCPath());
                        validationDetails.setErrorCount(errorLists.size());
                        validationDetails.setWarningCount(0);

                        validationDetailsList.add(validationDetails);
                    });

                    validationSummary.setValidationArtefacts(validationArtefactList);
                    validationSummary.setValidationDetails(validationDetailsList);
                    boolean validationRes = aErrors.containsNoError();
                    if (validationRes){
                        validationSummary.setIsValid(Boolean.TRUE);
                    }else {
                        validationSummary.setIsValid(Boolean.FALSE);
                    }
                } catch (Exception e) {
                    log.error("EXCEPTION :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource :: REASON :: {}", e.getMessage());
                    throw new ErrorListMappingException("Failed to Map Errors into Validation Summary : " + e.getMessage());
                }

                log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource :: FILE_NAME :: {} :: PINT_SPEC :: {} :: ARTEFACT_ID " +
                        ":: {} :: VERSION :: {} ::", multipartFile.getOriginalFilename(), pintSpec, docType, versionNo);

                return validationSummary;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
    }

    /**
     * Validates the provided XML string against the given PINT specification, document type, and version number.
     *
     * <p>This method performs the following steps:
     * <ul>
     *   <li>Logs the start of the validation process along with the provided specification, document type, and version.</li>
     *   <li>If the XML string is not empty, it converts the XML string into a {@link MultipartFile} and delegates the validation to the overloaded method {@code validateXMLSource(MultipartFile, String, String, String)}.</li>
     *   <li>Throws a {@link RuntimeException} if the XML string is empty.</li>
     *   <li>Logs the end of the validation process.</li>
     * </ul>
     * </p>
     *
     * @param xmlString the XML string to be validated, must not be blank or null.
     * @param pintSpec the PINT specification version to be used for validation, must not be blank or null.
     * @param docType the document type identifier, must not be blank or null.
     * @param versionNo the version of the document being validated, must not be blank or null.
     *
     * @return a {@link ValidationSummary} containing the results of the validation process.
     *
     * @throws RuntimeException if the XML string is empty.
     *
     * @see #convertXmlStringToMultipartFile(String, String)
     * @see #validateXMLSource(MultipartFile, String, String, String)
     * @see MultipartFile
     *
     *    <AUTHOR> Narayane
     *    @since 1.0
     *    @version 1.0
     */
    @Override
    public ValidationSummary validateXMLSource(
            @NotBlank(message = ValidationMessages.XML_NOT_NULL_OR_EMPTY) String xmlString,
            @NotBlank(message = ValidationMessages.PINT_SPEC_NOT_NULL_OR_EMPTY) String pintSpec,
            @NotBlank(message = ValidationMessages.DOCUMENT_TYPE_NOT_NULL_OR_EMPTY) String docType,
            @NotBlank(message = ValidationMessages.VERSION_NOT_NULL_OR_EMPTY) String versionNo) {


            log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource(XMLString) :: PINT_SPEC :: {} :: ARTEFACT_ID " +
                    ":: {} :: VERSION :: {} ::", pintSpec, docType, versionNo);

            if(!xmlString.isEmpty()){
                log.info("INTERMEDIATE :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource(XMLString) :: Converting XML String to MultiPart File");

                MultipartFile multipartFile = convertXmlStringToMultipartFile(xmlString, "xmlDDoc");

                log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource(XMLString) :: PINT_SPEC :: {} :: ARTEFACT_ID " +
                        ":: {} :: VERSION :: {} ::", pintSpec, docType, versionNo);
                return validateXMLSource(multipartFile, pintSpec, docType, versionNo);
            }else {
                log.error("EXCEPTION :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLSource(XMLString)");
                throw new RuntimeException("Input XML String is Empty");
            }
    }



    /*
        NOTE : THe below methods are under progress, If Schenatron based validation is completed, this will be removed.
    * */




    /**
     * Helper Method to Convert XML string to MultipartFile */
    public static MultipartFile convertXmlStringToMultipartFile(String xmlString, String fileName) {
        log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: convertXmlStringToMultipartFile ");

        byte[] xmlBytes = xmlString.getBytes();

        log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: convertXmlStringToMultipartFile ");

        return new CustomMultipartFile(fileName + ".xml", "application/xml", xmlBytes);
    }

    /**
     * This method checks if the provided XML file contains a "Standard Business Document" (SBD) or
     * "Standard Business Document Header" (SBDH). It parses the XML file and searches for these
     * elements based on their tag names, ignoring namespaces.
     *
     * <p>Steps performed by this method:
     * <ul>
     *     <li>Parses the input XML file to create a DOM representation.</li>
     *     <li>Searches for the <code>StandardBusinessDocument</code> and
     *         <code>StandardBusinessDocumentHeader</code> elements within the XML file.</li>
     *     <li>Returns <code>true</code> if either element is found, otherwise returns <code>false</code>.</li>
     * </ul>
     *
     * @param originalDoc the XML Document to be checked for the presence of SBDH (Standard Business Document Header).
     * @return <code>true</code> if the file contains either the <code>StandardBusinessDocument</code> or
     *         <code>StandardBusinessDocumentHeader</code> elements, otherwise <code>false</code>.
     * <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */
    public Boolean isSBDH(Document originalDoc) {

            log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: isSBDH ");

            // Step 1: Locate the <Invoice> element by its namespace and local name
            NodeList sbdNode = originalDoc.getElementsByTagNameNS("*", "StandardBusinessDocument");
            NodeList sbdhNode = originalDoc.getElementsByTagNameNS("*", "StandardBusinessDocumentHeader");

            Boolean isSBDH;
            if(sbdNode.getLength()==0 && sbdhNode.getLength()==0){
                log.info("INTERMEDIATE :: CLASS :: ValidatorHandlerImpl :: METHOD :: isSBDH :: Returning False");
                log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: isSBDH");
                isSBDH = Boolean.FALSE;
            }else {
                log.info("INTERMEDIATE :: CLASS :: ValidatorHandlerImpl :: METHOD :: isSBDH :: Returning TRUE");
                log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: isSBDH");
                isSBDH = Boolean.TRUE;
            }
            return isSBDH;
    }



    /**
     * Extracts the business document from a Standard Business Document Header (SBDH) XML file.
     * <p>
     * This method processes the provided XML file to locate and extract the business document
     * within the Standard Business Document Header (SBDH) structure.
     * </p>
     *
     *  @param originalDoc the XML Document
     * @return the {@link Node} representing the extracted business document
     * @throws Exception if any parsing or extraction errors occur during the process
     *
     *   <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */
    @Override
    public Node extractBusinessDocumentFromSBDH(Document originalDoc) throws Exception {

        log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: extractBusinessDocumentFromSBDH");

            NodeList docElementList = null;


            for (ElementTagNames tag : ElementTagNames.values()) {
                docElementList = originalDoc.getElementsByTagNameNS("*", tag.getTagName());

                if (docElementList.getLength() != 0) {
                    log.info("INTERMEDIATE :: CLASS :: ValidatorHandlerImpl :: METHOD :: extractBusinessDocumentFromSBDH :: Found <{}> element.", tag.getTagName());
                    break; // Exit loop if the element is found
                }

            }
            if (docElementList.getLength() == 0) {
                log.error("EXCEPTION :: CLASS :: ValidatorHandlerImpl :: METHOD :: extractBusinessDocumentFromSBDH ::The incoming XML Document is not a Valid XML Document");
                throw new Exception("The incoming XML Document is not a Valid XML Document");
            }

            Element docElement = (Element) docElementList.item(0);

            DocumentBuilderFactory newDocFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder newDocBuilder = newDocFactory.newDocumentBuilder();
            Document newDoc = newDocBuilder.newDocument();

            Node importedInvoice = newDoc.importNode(docElement, true);
            newDoc.appendChild(importedInvoice);

            log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: extractBusinessDocumentFromSBDH ");
            return importedInvoice;

    }


    /**
     * Converts a {@link MultipartFile} into a {@link Document} object by parsing the XML content.
     * <p>
     * This method uses {@link DocumentBuilderFactory} to create a {@link DocumentBuilder} and parses
     * the content of the multipart file into a {@link Document} object.
     * </p>
     *
     * @param multipartFile the {@link MultipartFile} containing XML content to be parsed.
     * @return a {@link Document} object representing the parsed XML content of the file.
     * @throws ParserConfigurationException if a DocumentBuilder cannot be created with the given configuration.
     * @throws IOException if an I/O error occurs during file parsing, such as reading the file stream.
     */
    public Document convertMultipartFileToDocument(MultipartFile multipartFile) throws ParserConfigurationException, IOException {
        log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: convertMultipartFileToDocument");

        // Step 1: Configure the DocumentBuilderFactory and DocumentBuilder
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);  // If your XML contains namespaces
        DocumentBuilder builder = factory.newDocumentBuilder();

        // Step 3: Parse the file to a Document object
        Document document = null;
        try (InputStream fileInputStream = multipartFile.getResource().getInputStream()) {
            document = builder.parse(fileInputStream);
        } catch (IOException | SAXException e) {
            log.error("EXCEPTION :: CLASS :: ValidatorHandlerImpl :: METHOD :: convertMultipartFileToDocument :: Error while parsing XML file: {}", e.getMessage(), e);
            throw new IOException("Error parsing the XML file : "+e.getMessage());
        }

        log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: convertMultipartFileToDocument");
        return document;
    }





    /**
     * Validates the provided XML file for compliance with the Standard Business Document Header (SBDH) structure.
     * <p>
     * This method checks whether the given XML file conforms to the expected SBDH format and content.
     * If the XML file does not comply with the SBDH structure, appropriate validation errors will be raised.
     * </p>
     *
     * @param   document the XML Document to be validate
     * @throws Exception if the validation process encounters errors or if the XML file is not valid
     *
     *   <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */
    @Override
    public void validateSBDHsource(Document document) throws Exception {

        log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateSBDHsource ");
        List<String> errorList = validateXMLStructure(document);
        if (!errorList.isEmpty()){
            for (String s : errorList){
                log.error("Error : {}", s);
            }
        }
        log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateSBDHsource ");
    }

    /**
     * Validates the structure of the provided XML {@link Document}.
     * <p>
     * This method checks the structure of the given XML document to ensure it conforms
     * to the required format and structure. It returns a list of validation error messages
     * if any issues are found in the document's structure.
     * </p>
     *
     * @param document the XML {@link Document} to be validated
     * @return a list of {@link String} containing validation error messages, or an empty list if the XML structure is valid
     *
     *   <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */
    public List<String> validateXMLStructure(Document document) {
        log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLStructure :: Received Document : {}", document.getDocumentElement().getNodeName());
        List<String> errorList = new ArrayList<>();


        // Step 1 : Check if StandardBusinessDocument element is present or not.

        NodeList standardBusinessDocumentList = document.getElementsByTagName("StandardBusinessDocument");
        if (standardBusinessDocumentList.getLength() == 0) {
            errorList.add("StandardBusinessDocument element is missing.");
            return errorList;
        }

        
        // Step 3: Check for the HeaderVersion element and its value
        NodeList headerVersionList = document.getElementsByTagName("HeaderVersion");
        if (headerVersionList.getLength() == 0) {
            errorList.add("HeaderVersion element is missing.");
            return errorList;
        } else {
            Element headerVersionElement = (Element) headerVersionList.item(0);
            String headerVersionValue = headerVersionElement.getTextContent();

            // Step 4: Validate the value of HeaderVersion
            if (!ValidationRulesAndPatterns.HEADER_VERSION_PATTERN.matcher(headerVersionValue).matches()) {
                log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLStructure ");
                errorList.add("HeaderVersion value is incorrect. Expected '1.0'.");
                return errorList;
            }
        }








        // Step 5: Check that the <Sender> element exists after <HeaderVersion>
        NodeList senderList = document.getElementsByTagName("Sender");
        if (senderList.getLength() == 0) {
             errorList.add("Error: Sender element is missing.");
             return errorList;
        }else{
            // Iterate through each <Sender>
            for (int i = 0; i < senderList.getLength(); i++) {

                Element senderElement = (Element) senderList.item(i);

                //Validate for Allowed Elements In Sender :
                boolean senderValidate = validateAllowedElements(senderElement, ValidationRulesAndPatterns.SENDER_ALLOWED_ELEMENTS, errorList);
                log.error("Sender Element Validate? : {}", senderValidate);

                if (!senderValidate) {
                    // Get <Identifier> from <Sender>
                    NodeList identifierNodeList = senderElement.getElementsByTagName("Identifier");

                    if (identifierNodeList.getLength() == 0) {

                        errorList.add("Error: Identifier element is missing under Sender Element.");
                        return errorList;
                    }else if (identifierNodeList.getLength() > 1) {

                        errorList.add("Error: Identifier element Should Be Present Only Once.");
                        return errorList;
                    }else {     //TODO : Check Value
                        //if (!IDENTIFIER_PATTERN.matcher(senderIdentifierValue).matches()) {
                        //   errorList.add("Error: <Sender><Identifier> value is incorrect. Expected format '0088:XXXXXXXXXXXXX'.");
                        //     return errorList;
                        //}
                    }


                    // Get <Authority> from <Sender>
                    //Rule : 0..1
                    NodeList authorityNodeList = senderElement.getElementsByTagName("Authority");

                    if (authorityNodeList.getLength() > 1) {
                        errorList.add("Error: Authority element Should Be Present Only Once.");
                        return errorList;
                    }

                    // Get <ContactInformation> from <Sender>
                    //Rule : 0..*
                    NodeList contactInformationNodeList = senderElement.getElementsByTagName("ContactInformation");

                    for (int j = 0; j < contactInformationNodeList.getLength(); j++) {

                        Element contactInfoElement = (Element) contactInformationNodeList.item(j);

                        //Validate for Allowed Elements In Sender :
                        boolean contactInfoValidate = validateAllowedElements(contactInfoElement, ValidationRulesAndPatterns.CONTACT_INFO_ELEMENTS, errorList);
                        log.error("ContactInfoValidate Element Validate? : {}", contactInfoValidate);

                        // Get <Contact> from <ContactInformation>
                        // Rule : 0..1
                        NodeList contactNodeList = contactInfoElement.getElementsByTagName("Contact");

                        if (contactNodeList.getLength() > 1) {
                            errorList.add("Error: Contact element Should Be Present Only Once.");
                            return errorList;
                        }
                    }
                }else {
                    return errorList;
                }

            }//for

        }//Sender





        // Step 7: Check for the <Receiver> element and validate Identifier
        NodeList receiverList = document.getElementsByTagName("Receiver");
        if (receiverList.getLength() == 0) {
             errorList.add("Error: <Receiver> element is missing.");
             return errorList;
        }

        // Step 8: Validate the Identifier in Receiver
        Element receiverIdentifierElement = (Element) document.getElementsByTagName("Identifier").item(1); // Receiver Identifier is the second one
        String receiverIdentifierValue = receiverIdentifierElement.getTextContent();
//        if (!IDENTIFIER_PATTERN.matcher(receiverIdentifierValue).matches()) {
//             errorList.add("Error: <Receiver><Identifier> value is incorrect. Expected format '0088:XXXXXXXXXXXXX'.");
//             return errorList;
//        }

        // Step 9: Check that DocumentIdentification elements are present
        NodeList documentIdentificationList = document.getElementsByTagName("DocumentIdentification");

        // Ensure there's at least one DocumentIdentification element
        if (documentIdentificationList.getLength() > 0) {
            Element documentIdentification = (Element) documentIdentificationList.item(0);

            // Step 2: Check child nodes
            NodeList childNodes = documentIdentification.getChildNodes();
            boolean hasStandard = false;
            boolean hasTypeVersion = false;
            boolean hasInstanceIdentifier = false;
            boolean hasType = false;
            boolean hasCreationDateAndTime = false;

            for (int i = 0; i < childNodes.getLength(); i++) {
                Node child = childNodes.item(i);

                if (child.getNodeType() == Node.ELEMENT_NODE) {
                    String elementName = child.getNodeName();

                    // Check if the child element matches any of the expected names
                    if ("Standard".equals(elementName)) {
                        hasStandard = true;
                        validateElementText(child, "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2", errorList);
                    } else if ("TypeVersion".equals(elementName)) {
                        hasTypeVersion = true;
                    } else if ("InstanceIdentifier".equals(elementName)) {
                        hasInstanceIdentifier = true;
                    } else if ("Type".equals(elementName)) {
                        hasType = true;
                    } else if ("CreationDateAndTime".equals(elementName)) {
                        hasCreationDateAndTime = true;
                    }
                }
            }

            if (!hasStandard){
                errorList.add("Standard element should be present in <DocumentIdentification> element");
            }
            if (!hasTypeVersion){
                errorList.add("TypeVersion element should be present in <DocumentIdentification> element");
            }
            if (!hasInstanceIdentifier){
                errorList.add("InstanceIdentifier element should be present in <DocumentIdentification> element");
            }
            if (!hasType){
                errorList.add("Type element should be present in <DocumentIdentification> element");
            }
            if (!hasCreationDateAndTime){
                errorList.add("CreationDateAndTime element should be present in <DocumentIdentification> element ");
            }

        } else {
            errorList.add("No <DocumentIdentification> element found.");
        }



        // Step 11: Validate CreationDateAndTime
        NodeList creationDateList = document.getElementsByTagName("CreationDateAndTime");
        if (creationDateList.getLength() == 0) {
             errorList.add("ERROR: <CreationDateAndTime> element is missing.");
             return errorList;
        } else {
            Element creationDateElement = (Element) creationDateList.item(0);
            String creationDateValue = creationDateElement.getTextContent();
            if (!ValidationRulesAndPatterns.TIMESTAMP_PATTERN.matcher(creationDateValue).matches()) {
                 errorList.add("ERROR: <CreationDateAndTime> value is incorrect. Expected format 'YYYY-MM-DDThh:mm:ssZ'.");
                 return errorList;
            }
        }

        // Step 12: Validate BusinessScope and Scope elements
        NodeList scopeList = document.getElementsByTagName("Scope");
        if (scopeList.getLength() == 0) {
             errorList.add("ERROR: <Scope> element is missing within <BusinessScope>.");
             return errorList;
        }

        validateScopeElement(document, "DOCUMENTID", "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:www.cenbii.eu:transaction:biitrns010:ver2.0:extended:urn:www.peppol.eu:bis:peppol4a:ver2.0::2.1");
        validateScopeElement(document, "PROCESSID", "urn:www.cenbii.eu:profile:bii04:ver1.0");
        validateScopeElement(document, "COUNTRY_C1", "AT");

        // errorList.add("Validation passed: XML is structured correctly.");

        log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateXMLStructure ");


        return errorList;
    }




    // Recursive method to validate allowed elements and sub-elements
    private static boolean validateAllowedElements(Element parentElement, Set<String> allowedElements,  List<String> errorList) {
        NodeList childNodes = parentElement.getChildNodes();

        for (int i = 0; i < childNodes.getLength(); i++) {
            Node node = childNodes.item(i);

            if (node.getNodeType() == Node.ELEMENT_NODE) {
                String nodeName = node.getNodeName();

                if (!allowedElements.contains(nodeName)) {
                    errorList.add("Error: Invalid element <" + nodeName + "> found under <" + parentElement.getNodeName() + ">.");
                    return false;
                }
            }
        }
        return true;
    }






    /**
     * Validates the text content of an XML element against an expected value.
     * <p>
     * This method compares the text content of the provided XML {@link Node} (elementNode)
     * to the specified expected value. If the text content does not match the expected value,
     * an error message is added to the provided error list.
     * </p>
     *
     * @param elementNode   the XML {@link Node} representing the element whose text content is to be validated
     * @param expectedValue the expected text value to be compared against the element's text content
     * @param errorList     the list of error messages where validation failures will be added
     *
     *   <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */
    public static void validateElementText(Node elementNode, String expectedValue, List<String> errorList) {
        log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateElementText ");

        if (elementNode.getNodeType() == Node.ELEMENT_NODE) {
            // Get the text content of the node
            String textContent = elementNode.getTextContent().trim();

            // Compare the text content with the expected value
            if (expectedValue.equals(textContent)) {
                log.info("INTERMEDIATE :: CLASS :: ValidatorHandlerImpl :: METHOD :: VALIDATION_ELEMENT_TEXT :: Text inside <" + elementNode.getNodeName() + "> matches the expected value: " + expectedValue);
            } else {
                errorList.add("Text inside <" + elementNode.getNodeName() + "> does not match the expected value. Found: " + textContent);
            }
        }
        log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateElementText ");
    }

    /**
     * Validates the <Scope> elements within the provided XML {@link Document}.
     * <p>
     * This method checks for <Scope> elements in the provided XML document that match the given type
     * and expected instance identifier. It ensures that the <Scope> element contains the correct values.
     * </p>
     *
     * @param document                  the XML {@link Document} containing the <Scope> elements to be validated
     * @param type                      the type of <Scope> element to search for and validate
     * @param expectedInstanceIdentifier the expected instance identifier that the <Scope> element should contain
     *
     *   <AUTHOR> Narayane
     *   @since 1.0
     *   @version 1.0
     */
    private static void validateScopeElement(Document document, String type, String expectedInstanceIdentifier) {
        log.info("START :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateScopeElement ");
        NodeList scopeList = document.getElementsByTagName("Scope");
        for (int i = 0; i < scopeList.getLength(); i++) {
            Element scopeElement = (Element) scopeList.item(i);
            String scopeType = scopeElement.getElementsByTagName("Type").item(0).getTextContent();
            if (scopeType.equals(type)) {
                String instanceIdentifier = scopeElement.getElementsByTagName("InstanceIdentifier").item(0).getTextContent();
                if (!instanceIdentifier.equals(expectedInstanceIdentifier)) {
                    log.error("ERROR :: CLASS :: ValidatorHandlerImpl :: METHOD :: VALIDATION_ELEMENT_TEXT ::  <Scope><InstanceIdentifier> value for type '" + type + "' is incorrect.");
                }
            }
        }
        log.info("END :: CLASS :: ValidatorHandlerImpl :: METHOD :: validateScopeElement ");
    }

}
