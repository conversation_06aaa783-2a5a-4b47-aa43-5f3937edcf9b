package com.perennialsys.peppol.validation.handler;


import com.perennialsys.peppol.validation.dto.ValidationSummary;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import java.io.File;
import java.io.IOException;

public interface IValidatorHandler {

   ValidationSummary validateXMLSource(MultipartFile file,
                                              String groupId,
                                              String artefactId,
                                              String versionNo) throws IOException;

   ValidationSummary validateXMLSource(String xmlString,
                                       String groupId,
                                       String artefactId,
                                       String versionNo) throws IOException;

   Node extractBusinessDocumentFromSBDH(Document originalDoc) throws Exception;



   void validateSBDHsource(Document document) throws Exception;


}
