package com.perennialsys.peppol.validation.dto;

import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


@XmlRootElement(name = "StandardBusinessDocument", namespace = "http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader")
@XmlType(propOrder = {"standardBusinessDocumentHeader"})
public class StandardBusinessDocument{
    private StandardBusinessDocumentHeader standardBusinessDocumentHeader;

    @XmlElement(name = "StandardBusinessDocumentHeader")
    public StandardBusinessDocumentHeader getStandardBusinessDocumentHeader() {
        return standardBusinessDocumentHeader;
    }

    public void setStandardBusinessDocumentHeader(StandardBusinessDocumentHeader standardBusinessDocumentHeader) {
        this.standardBusinessDocumentHeader = standardBusinessDocumentHeader;
    }


    @Override
    public String toString() {
        return "StandardBusinessDocument{" +
                "standardBusinessDocumentHeader=" + standardBusinessDocumentHeader+
                '}';
    }
}


@XmlType(propOrder = {"headerVersion", "sender", "receiver", "documentIdentification", "businessScope"})
class StandardBusinessDocumentHeader  {
    private String headerVersion;
    private Sender sender;
    private Receiver receiver;
    private DocumentIdentification documentIdentification;
    private BusinessScope businessScope;


    @XmlElement(name = "HeaderVersion")
    public String getHeaderVersion() {
        return headerVersion;
    }

    public void setHeaderVersion(String headerVersion) {
        this.headerVersion = headerVersion;
    }


    @XmlElement(name = "Sender")
    public Sender getSender() {
        return sender;
    }

    public void setSender(Sender sender) {
        this.sender = sender;
    }

    @XmlElement(name = "Receiver")
    public Receiver getReceiver() {
        return receiver;
    }

    public void setReceiver(Receiver receiver) {
        this.receiver = receiver;
    }

    @XmlElement(name = "DocumentIdentification")
    public DocumentIdentification getDocumentIdentification() {
        return documentIdentification;
    }

    public void setDocumentIdentification(DocumentIdentification documentIdentification) {
        this.documentIdentification = documentIdentification;
    }

    @XmlElement(name = "BusinessScope")
    public BusinessScope getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(BusinessScope businessScope) {
        this.businessScope = businessScope;
    }


    @Override
    public String toString() {
        return "StandardBusinessDocumentHeader{" +
                "headerVersion='" + headerVersion + '\'' +
                ", sender=" + sender +
                ", receiver=" + receiver +
                ", documentIdentification=" + documentIdentification+
                ", businessScope=" + businessScope+
                '}';
    }
}

@Data
class Sender {
    private String authority;
    private String identifier;

    @Override
    public String toString() {
        return "Sender{" +
                "authority='" + authority + '\'' +
                ", identifier='" + identifier + '\'' +
                '}';
    }
}

@Data
class Receiver {
    private String authority;
    private String identifier;
}

@Data
class DocumentIdentification {
    private String standard;
    private String typeVersion;
    private String instanceIdentifier;
    private String type;
    private LocalDateTime creationDateAndTime;
}

@Data
class BusinessScope {
    private List<Scope> scopes;
}

@Data
class Scope {
    private String type;
    private String instanceIdentifier;
    private String identifier;
}

