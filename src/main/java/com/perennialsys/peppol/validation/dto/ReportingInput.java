package com.perennialsys.peppol.validation.dto;

import java.time.OffsetDateTime;

public class ReportingInput {
    private OffsetDateTime m_aExchangeDTUTC;
    private String m_eDirection;
    private String m_sC2ID;
    private String m_sC3ID;
    private String m_sDocTypeIDScheme;
    private String m_sDocTypeIDValue;
    private String m_sProcessIDScheme;
    private String m_sProcessIDValue;
    private String m_sTransportProtocol;
    private String m_sC1CountryCode;
    private String m_sC4CountryCode;
    private String m_sEndUserID;

    public ReportingInput() {
    }


    public OffsetDateTime getM_aExchangeDTUTC() {
        return m_aExchangeDTUTC;
    }

    public void setM_aExchangeDTUTC(OffsetDateTime m_aExchangeDTUTC) {
        this.m_aExchangeDTUTC = m_aExchangeDTUTC;
    }


    public String getM_eDirection() {
        return m_eDirection;
    }

    public void setM_eDirection(String m_eDirection) {
        this.m_eDirection = m_eDirection;
    }

    public String getM_sC2ID() {
        return m_sC2ID;
    }

    public void setM_sC2ID(String m_sC2ID) {
        this.m_sC2ID = m_sC2ID;
    }

    public String getM_sC3ID() {
        return m_sC3ID;
    }

    public void setM_sC3ID(String m_sC3ID) {
        this.m_sC3ID = m_sC3ID;
    }

    public String getM_sDocTypeIDScheme() {
        return m_sDocTypeIDScheme;
    }

    public void setM_sDocTypeIDScheme(String m_sDocTypeIDScheme) {
        this.m_sDocTypeIDScheme = m_sDocTypeIDScheme;
    }

    public String getM_sDocTypeIDValue() {
        return m_sDocTypeIDValue;
    }

    public void setM_sDocTypeIDValue(String m_sDocTypeIDValue) {
        this.m_sDocTypeIDValue = m_sDocTypeIDValue;
    }

    public String getM_sProcessIDScheme() {
        return m_sProcessIDScheme;
    }

    public void setM_sProcessIDScheme(String m_sProcessIDScheme) {
        this.m_sProcessIDScheme = m_sProcessIDScheme;
    }

    public String getM_sProcessIDValue() {
        return m_sProcessIDValue;
    }

    public void setM_sProcessIDValue(String m_sProcessIDValue) {
        this.m_sProcessIDValue = m_sProcessIDValue;
    }

    public String getM_sTransportProtocol() {
        return m_sTransportProtocol;
    }

    public void setM_sTransportProtocol(String m_sTransportProtocol) {
        this.m_sTransportProtocol = m_sTransportProtocol;
    }

    public String getM_sC1CountryCode() {
        return m_sC1CountryCode;
    }

    public void setM_sC1CountryCode(String m_sC1CountryCode) {
        this.m_sC1CountryCode = m_sC1CountryCode;
    }

    public String getM_sC4CountryCode() {
        return m_sC4CountryCode;
    }

    public void setM_sC4CountryCode(String m_sC4CountryCode) {
        this.m_sC4CountryCode = m_sC4CountryCode;
    }

    public String getM_sEndUserID() {
        return m_sEndUserID;
    }

    public void setM_sEndUserID(String m_sEndUserID) {
        this.m_sEndUserID = m_sEndUserID;
    }


    @Override
    public String toString() {
        return "ReportingInput{" +
                "m_aExchangeDTUTC=" + m_aExchangeDTUTC +
                ", m_eDirection='" + m_eDirection + '\'' +
                ", m_sC2ID='" + m_sC2ID + '\'' +
                ", m_sC3ID='" + m_sC3ID + '\'' +
                ", m_sDocTypeIDScheme='" + m_sDocTypeIDScheme + '\'' +
                ", m_sDocTypeIDValue='" + m_sDocTypeIDValue + '\'' +
                ", m_sProcessIDScheme='" + m_sProcessIDScheme + '\'' +
                ", m_sProcessIDValue='" + m_sProcessIDValue + '\'' +
                ", m_sTransportProtocol='" + m_sTransportProtocol + '\'' +
                ", m_sC1CountryCode='" + m_sC1CountryCode + '\'' +
                ", m_sC4CountryCode='" + m_sC4CountryCode + '\'' +
                ", m_sEndUserID='" + m_sEndUserID + '\'' +
                '}';
    }
}
